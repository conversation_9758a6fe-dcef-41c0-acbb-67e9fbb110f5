{
  "asar": false,
  "extraFiles": [],
  "publish": [
    {
      "provider": "generic",
      "url": "http://127.0.0.1"
    }
  ],
  "productName": "译翻翻",
  "appId": "com.ypctzz.client",
  "directories": {
    "output": "build"
  },
  "files": [
    "dist/electron/**/*"
  ],
  "dmg": {
    "contents": [
      {
        "x": 410,
        "y": 150,
        "type": "link",
        "path": "/Applications"
      },
      {
        "x": 130,
        "y": 150,
        "type": "file"
      }
    ]
  },
  "mac": {
    "icon": "resources/icons/icon.icns" ,
    "entitlements": "resources/entitlements.mac.plist",
    "entitlementsInherit": "resources/entitlements.mac.plist",
     "extendInfo": {
      "NSMicrophoneUsageDescription": "我们需要使用麦克风来采集音频"
    },
    "target": [
      "pkg",
      "dmg",
      "zip"
    ]
  },
  "win": {
    "icon": "resources/icons/icon.ico",
    "requestedExecutionLevel": "requireAdministrator" //管理员权限运行
  },
  "linux": {
    "icon": "resources/icons"
  }
}