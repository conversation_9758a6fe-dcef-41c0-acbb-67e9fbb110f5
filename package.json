{"name": "yff", "version": "0.0.2", "author": "sky <https://github.com/umbrella22>", "description": "An electron-vue project", "license": "MIT", "main": "./dist/electron/main/main.js", "scripts": {"dev": "tsx .electron-vue/dev-runner.ts", "dev:sit": "tsx .electron-vue/dev-runner.ts -m sit", "build": "tsx .electron-vue/build.ts && electron-builder -c build.json", "build:win32": "tsx .electron-vue/build.ts && electron-builder -c build.json --win  --ia32", "build:win64": "tsx .electron-vue/build.ts && electron-builder -c build.json --win  --x64", "build:mac": "tsx .electron-vue/build.ts && electron-builder -c build.json --mac", "build:dir": "tsx .electron-vue/build.ts && electron-builder -c build.json --dir", "build:clean": "tsx .electron-vue/build.ts --clean", "build:web": "tsx .electron-vue/build.ts --target web", "pack:resources": "tsx .electron-vue/hot-updater.ts", "dep:upgrade": "yarn upgrade-interactive --latest", "postinstall": "electron-builder install-app-deps", "unocss:build": "unocss src/renderer/**/*.{vue,ts,js,tsx,jsx,html} src/renderer/views/components/*.{vue,ts,js,tsx,jsx,html}  -o src/renderer/unocss.generated.css --watch", "build:mac-pkg": "export ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/ && tsx .electron-vue/build.ts && electron-builder -c build.json --mac -p never"}, "engines": {"node": ">=20.17.0", "npm": ">=10.9.0"}, "dependencies": {"@fingerprintjs/fingerprintjs": "^4.6.2", "better-sqlite3": "^11.10.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "electron-updater": "^6.6.2", "element-plus": "^2.10.2", "fluent-ffmpeg": "^2.1.3", "jszip": "^3.10.1", "microsoft-cognitiveservices-speech-sdk": "^1.44.1", "node-fetch": "^3.3.2", "pinyin-pro": "^3.26.0", "vue-clipboard3": "^2.0.0"}, "devDependencies": {"@electron/osx-sign": "^1.3.1", "@ikaros-cli/prettier-config": "^0.0.2", "@ikaros-cli/stylelint-config": "^0.0.2", "@rspack/core": "^1.3.15", "@rspack/dev-server": "^1.1.0", "@types/adm-zip": "^0.5.7", "@types/fs-extra": "^11.0.4", "@types/node": "^22.13.1", "@types/semver": "^7.7.0", "@unocss/webpack": "^66.1.2", "adm-zip": "^0.5.16", "axios": "^1.8.4", "brace-expansion": "^1.1.12", "cfonts": "^3.3.0", "chalk": "^5.4.1", "css-loader": "^7.1.2", "del": "^8.0.0", "detect-port": "^2.1.0", "dotenv": "^16.4.7", "electron": "^37.1.0", "electron-builder": "^26.0.12", "electron-devtools-vendor": "^3.0.0", "fs-extra": "^11.3.0", "listr2": "^8.2.5", "minimatch": "^7.4.6", "minimist": "^1.2.8", "pinia": "3.0.1", "sass-embedded": "^1.86.0", "sass-loader": "^16.0.5", "style-loader": "^4.0.0", "tsx": "^4.19.3", "typescript": "^5.8.2", "unocss": "^66.1.2", "unplugin": "^2.3.5", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vue": "^3.5.13", "vue-loader": "^17.4.2", "vue-router": "^4.5.0", "vue-style-loader": "^4.1.3"}}