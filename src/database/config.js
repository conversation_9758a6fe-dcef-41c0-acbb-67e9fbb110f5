// src/main/config.js
import { app } from 'electron'
import path from 'path'

export const getStoragePath = () => {
  // 存储在用户文档目录下的 MyAppFiles 文件夹
  const docsPath = app.getPath('userData')
  return path.join(docsPath, 'MyAppFiles')
}

export const ensureStorageDir = () => {
  const fs = require('fs')
  const storagePath = getStoragePath()
  if (!fs.existsSync(storagePath)) {
    fs.mkdirSync(storagePath, { recursive: true })
  }
  return storagePath
}