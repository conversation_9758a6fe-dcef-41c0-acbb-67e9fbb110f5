import Database from 'better-sqlite3';
import path from 'path';
import { app } from 'electron';
import fs from 'fs';

class SQLiteDB {
  constructor() {
    // 数据库文件存放在用户数据目录
    this.dbPath = path.join(app.getPath('userData'), 'app.db');
    this.ensureDbDirExists();
    this.db = new Database(this.dbPath);
    this.db.pragma('journal_mode = WAL'); // 提高并发性能
    this.initTables();
  }

  // 确保数据库目录存在
  ensureDbDirExists() {
    const dir = path.dirname(this.dbPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  }

  // 初始化数据表
  initTables() {
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        folder_path TEXT NOT NULL,
        audio_path TEXT NOT NULL,
        txt_path TEXT NOT NULL,
        txt_timestamp_path TEXT NOT NULL,
        origin_file_name TEXT NOT NULL,
        file_name TEXT NOT NULL,
        duration INTEGER,
        type TEXT,
        is_uploaded INTEGER DEFAULT 0,
        source_language TEXT,
        target_language TEXT,
        duration_seconds INTEGER,
        created_at TEXT DEFAULT (strftime('%Y-%m-%d %H:%M:%S', 'now', 'localtime'))
      );
      
      CREATE INDEX IF NOT EXISTS idx_records_created ON records(created_at);
    `);
    const newFields = [
      { name: 'is_uploaded', type: 'INTEGER', def: '0' },
      { name: 'source_language', type: 'TEXT', def: "''" },
      { name: 'target_language', type: 'TEXT', def: "''" },
      { name: 'duration_seconds', type: 'INTEGER', def: '0' }
    ];
    // 批量检测并自动添加缺失字段
    for (const field of newFields) {
      try {
        this.db.prepare(`SELECT ${field.name} FROM records LIMIT 1`).get();
      } catch (error) {
        this.db.exec(`ALTER TABLE records ADD COLUMN ${field.name} ${field.type} DEFAULT ${field.def}`);
      }
    }
  }
  // 添加记录
  addRecord({ folder_path, audio_path, txt_path, txt_timestamp_path, origin_file_name, file_name, duration, type, is_uploaded = 0, created_at, source_language, target_language, duration_seconds }) {
    this.db.prepare(
      'INSERT INTO records (folder_path, audio_path, txt_path, txt_timestamp_path, origin_file_name, file_name, duration, type, is_uploaded, created_at, source_language, target_language, duration_seconds) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)'
    ).run(folder_path, audio_path, txt_path, txt_timestamp_path, origin_file_name, file_name, duration, type, is_uploaded, created_at, source_language, target_language, duration_seconds);
  }
  // 获取记录 分页获取
  getRecords({ fileName = '', type = '', page = 1, pageSize = 10 } = {}) {
    try {
      let sql = 'SELECT * FROM records';
      let countSql = 'SELECT COUNT(*) as total FROM records';
      const params = [];
      const countParams = [];
  
      const conditions = [];
      if (fileName) {
        conditions.push('file_name LIKE ?');
        params.push(`%${fileName}%`);
        countParams.push(`%${fileName}%`);
      }
      if (type) {
        conditions.push('type = ?');
        params.push(type);
        countParams.push(type);
      }
      if (conditions.length) {
        sql += ' WHERE ' + conditions.join(' AND ');
        countSql += ' WHERE ' + conditions.join(' AND ');
      }
  
      sql += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
      params.push(pageSize, (page - 1) * pageSize);
  
      const data = this.db.prepare(sql).all(...params);
      const { total } = this.db.prepare(countSql).get(...countParams);
  
      return {
        success: true,
        list: data,
        pagination: {
          total,
          page,
          pageSize
        }
      };
    } catch (e) {
      return { success: false, message: e.message };
    }
  }
  updateFileName({ id, newFileName }) {
    try {
      // 1. 查询原记录
      const record = this.db.prepare('SELECT * FROM records WHERE id = ?').get(id)
      if (!record) {
        return { success: false, message: '记录不存在' }
      }
      
      // const oldFilePath = path.join(record.local_path, record.file_name)
      // const newFilePath = path.join(record.local_path, newFileName)
  
      // // 2. 修改本地文件名
      // if (fs.existsSync(oldFilePath)) {
      //   fs.renameSync(oldFilePath, newFilePath)
      // } else {
      //   return { success: false, message: '本地文件不存在' }
      // }
  
      // 3. 更新数据库
      this.db.prepare('UPDATE records SET file_name = ? WHERE id = ?').run(newFileName, id)
  
      return { success: true }
    } catch (e) {
      return { success: false, message: e.message }
    }
  }
  // 更新上传状态
  updateUploadStatus({ id, isUploaded }) {
    try {
      const record = this.db.prepare('SELECT * FROM records WHERE id = ?').get(id)
      if (!record) {
        return { success: false, message: '记录不存在' }
      }

      this.db.prepare('UPDATE records SET is_uploaded = ? WHERE id = ?').run(isUploaded, id)
      return { success: true }
    } catch (e) {
      return { success: false, message: e.message }
    }
  }

  // 批量更新上传状态
  batchUpdateUploadStatus({ ids, isUploaded }) {
    try {
      const placeholders = ids.map(() => '?').join(',')
      this.db.prepare(`UPDATE records SET is_uploaded = ? WHERE id IN (${placeholders})`).run(isUploaded, ...ids)
      return { success: true }
    } catch (e) {
      return { success: false, message: e.message }
    }
  }
  // 删除记录
  deleteRecord(id) {
    try {
      // 1. 查询记录，获取 folder_path
      const record = this.db.prepare('SELECT * FROM records WHERE id = ?').get(id)
      if (!record) {
        return { success: false, message: '记录不存在' }
      }
  
      const folderPath = record.folder_path
      // 2. 删除数据库记录
      this.db.prepare('DELETE FROM records WHERE id = ?').run(id)
  
      // // 3. 递归删除本地文件夹及其内容
      // if (folderPath && fs.existsSync(folderPath)) {
      //   fs.rmSync(folderPath, { recursive: true, force: true })
      // }else{
      //   return { success: true, message: '数据库记录删除成功，本地文件夹不存在' }
      // }
      return { success: true }
    } catch (e) {
      return { success: false, message: e.message }
    }
  }
  // 关闭数据库连接
  close() {
    this.db.close();
  }
}

// 单例模式导出
export default new SQLiteDB();