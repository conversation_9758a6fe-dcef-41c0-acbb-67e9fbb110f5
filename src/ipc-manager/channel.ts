import type { ProgressInfo } from 'electron-updater'

export interface IpcMainEventListener<Send = void, Receive = void> {
  ipcMainHandle: Send extends void
    ? (event: Electron.IpcMainInvokeEvent) => Receive | Promise<Receive>
    : (
        event: Electron.IpcMainInvokeEvent,
        args: Send,
      ) => Receive | Promise<Receive>
  ipcRendererInvoke: Send extends void
    ? () => Promise<Receive>
    : (args: Send) => Promise<Receive>
}

export interface IpcRendererEventListener<Send = void> {
  ipcRendererOn: Send extends void
    ? (event: Electron.IpcRendererEvent) => void
    : (event: Electron.IpcRendererEvent, args: Send) => void
  webContentSend: Send extends void
    ? (webContents: Electron.WebContents) => void
    : (webContents: Electron.WebContents, args: Send) => void
}

export class IpcChannelMainClass {
  SetFloatWinAlwaysOnTop!: IpcMainEventListener<Boolean, void>
  WorkbenchWinToFloatWin!: IpcMainEventListener<any, void>
  CloseFloatWin!: IpcMainEventListener<void, void>
  OpenFloatWin!: IpcMainEventListener<{url: string}, void>
  GetEnvInfo!: IpcMainEventListener<void, any>
  DownloadFilesToHiddenFolder!: IpcMainEventListener<{u_s_id: string, cloud_id: string, files: {name: string, url?: string, base64?: string}[]}, {success: boolean, message?: string}>
  DownloadFileAsZip!: IpcMainEventListener<{ name: string, url?: string, base64?: string }[], {success: boolean, message?: string, filePath?: string}>
  UpdateUploadStatus!: IpcMainEventListener<{id: number, isUploaded: boolean}, {success: boolean, message?: string}>
  BatchUpdateUploadStatus!: IpcMainEventListener<{ids: number[], isUploaded: boolean}, {success: boolean, message?: string}>
  DeleteRecord!: IpcMainEventListener<{recordItem: any,type: string}, {success: boolean, message?: string}>
  ReadFile!: IpcMainEventListener<{filePath: string,type: string, audioFormat?: string}, {success: boolean, content?: string, message?: string}>
  UpdateFileName!: IpcMainEventListener<{id: number, newFileName: string}, {success: boolean, message?: string}>
  // 添加记录
  AddRecord!: IpcMainEventListener<{folder_path: string, audio_path: string, txt_path: string, txt_timestamp_path: string, origin_file_name: string, file_name: string, duration: number, type: string, created_at: string}, {success: boolean, message?: string}>
  // 获取记录
  GetHistory!: IpcMainEventListener<{ page: number, pageSize: number, fileName: string, type: string}, {success: boolean, records?: Object[], message?: string}>
 
  GetFilesInFolder!: IpcMainEventListener<{u_s_id: string, cloud: boolean}, {success: boolean, files?: string[], message?: string}>
  DownloadBlobFile!: IpcMainEventListener<{file: ArrayBuffer, fileName: string, u_s_id: string, filePathStr: string}, {success: boolean, message?: string, path?: string}>
  OpenTheFolder!: IpcMainEventListener<{u_s_id: string, filePath: string}, {success: boolean, message?: string}>
  CreateUserFolder!: IpcMainEventListener<string, {success: boolean, path?: string, message?: string}>
  DownloadOssFile!: IpcMainEventListener<{url: string, defaultName?: string}, {success: boolean, message?: string, path?: string}>
  CloseMainWindow!:IpcMainEventListener
  GetMediaAccessStatus!:IpcMainEventListener<void,boolean>
  OpenSystemSettings!:IpcMainEventListener
  IsUseSysTitle!: IpcMainEventListener<void, boolean>
  /**
   * 退出应用
   */
  AppClose!: IpcMainEventListener
  CheckUpdate!: IpcMainEventListener
  ConfirmUpdate!: IpcMainEventListener
  OpenMessagebox!: IpcMainEventListener<
    Electron.MessageBoxOptions,
    Electron.MessageBoxReturnValue
  >
  StartDownload!: IpcMainEventListener<string>
  OpenErrorbox!: IpcMainEventListener<{ title: string; message: string }>
  StartServer!: IpcMainEventListener<void, string>
  StopServer!: IpcMainEventListener<void, string>
  HotUpdate!: IpcMainEventListener
  InAppPurchase!: IpcMainEventListener<string, {success: boolean; message: string}>
  /**
   *
   * 打开窗口
   */
  OpenWin!: IpcMainEventListener<{
    /**
     * 新的窗口地址
     *
     * @type {string}
     */
    url: string

    /**
     * 是否是支付页
     *
     * @type {boolean}
     */
    IsPay?: boolean

    /**
     * 支付参数
     *
     * @type {string}
     */
    PayUrl?: string

    /**
     * 发送的新页面数据
     *
     * @type {unknown}
     */
    sendData?: unknown
  }>
}
export class IpcChannelRendererClass {
  // ipcRenderer
  DownloadProgress!: IpcRendererEventListener<number>
  DownloadError!: IpcRendererEventListener<Boolean>
  DownloadPaused!: IpcRendererEventListener<Boolean>
  DownloadDone!: IpcRendererEventListener<{
    /**
     * 下载的文件路径
     *
     * @type {string}
     */
    filePath: string
  }>
  updateMsg!: IpcRendererEventListener<{
    state: number
    msg: string | ProgressInfo
  }>
  UpdateProcessStatus!: IpcRendererEventListener<{
    status:
      | 'init'
      | 'downloading'
      | 'moving'
      | 'finished'
      | 'failed'
      | 'download'
    message: string
  }>

  SendDataTest!: IpcRendererEventListener<unknown>
  purchaseStatus!: IpcRendererEventListener<{
    status: string,
    msg: string,
    productId: string
  }>
  WorkbenchToFloat!: IpcRendererEventListener<void>
  SetFloatWinSwitchStatus!: IpcRendererEventListener<boolean>
  BrowserViewTabDataUpdate!: IpcRendererEventListener<{
    bvWebContentsId: number
    title: string
    url: string
    status: 1 | -1 // 1 添加/更新 -1 删除
  }>
  BrowserViewTabPositionXUpdate!: IpcRendererEventListener<{
    dragTabOffsetX: number
    positionX: number
    bvWebContentsId: number
  }>
  BrowserTabMouseup!: IpcRendererEventListener
  HotUpdateStatus!: IpcRendererEventListener<{
    status: string
    message: string
  }>
}
