'use strict'

import { useMainDefaultIpc } from './services/ipc-main'
import { app, session } from 'electron'
import mainInit from './services/window-manager'
import { useDisableButton } from './hooks/disable-button-hook'
import { useProcessException } from '@main/hooks/exception-hook'
import { useMenu } from '@main/hooks/menu-hook'
import InstallVirtualAudioCapture from "./utils/InstallVirtualAudioCapture"
import AudioRecorder from './utils/AudioRecorder'
import SQLiteDB from '../database/db'
const db = SQLiteDB
async function onAppReady() {
  if (process.platform === 'darwin') {
    console.log('当前操作系统为Mac')
    // 在这里处理Mac特有的逻辑
  } else if (process.platform === 'win32') {
    console.log('当前操作系统为Windows')
    try {
       InstallVirtualAudioCapture("virtual-audio-capturer", true)
    } catch (err:any) {
      console.error('捕获到错误:', err)
    }
  }
  const { disableF12 } = useDisableButton()
  const { renderProcessGone } = useProcessException()
  const { defaultIpc } = useMainDefaultIpc()
  const { creactMenu } = useMenu()
  disableF12()
  renderProcessGone()
  defaultIpc()
  creactMenu()
  AudioRecorder.init();
  mainInit.initWindow()
  // if (import.meta.env.NODE_ENV === 'development') {
  //   const { VUEJS3_DEVTOOLS } = require('electron-devtools-vendor')
  //   session.defaultSession.loadExtension(VUEJS3_DEVTOOLS, {
  //     allowFileAccess: true,
  //   })
  //   console.log('已安装: vue-devtools')
  // }
  // session.defaultSession.setPermissionRequestHandler(
  //   (webContents, permission, callback) => {
  //     console.log('permission------', permission)
  //     if (permission === 'media' || permission === 'microphone') {
  //       callback(true) // 允许媒体权限
  //     } else {
  //       callback(false) // 拒绝其他权限
  //     }
  //   },
  // )
}

app.whenReady().then(onAppReady)
// 由于9.x版本问题，需要加入该配置关闭跨域问题
app.commandLine.appendSwitch('disable-features', 'OutOfBlinkCors')

app.on('window-all-closed', () => {
  // 所有平台均为所有窗口关闭就退出软件
  app.quit()
})
app.on('browser-window-created', () => {
  console.log('window-created')
})

// if (process.defaultApp) {
//   if (process.argv.length >= 2) {
//     app.removeAsDefaultProtocolClient('electron-vue-template')
//     console.log('由于框架特殊性开发环境下无法使用')
//   }
// } else {
//   app.setAsDefaultProtocolClient('electron-vue-template')
// }
