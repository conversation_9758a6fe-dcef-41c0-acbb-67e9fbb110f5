import { dialog, BrowserWindow, app, inAppPurchase, systemPreferences, shell } from 'electron'
import fs from 'fs'
import path from 'path'
import https from 'https'
import http from 'http'
import { getPreloadFile, winURL } from '../config/static-path'
import { updater } from '../services/hot-updater'
import DownloadFile from '../services/download-file'
import Update from '../services/check-update'
import config from '@config/index'
import JSZip from 'jszip'
import { IIpcMainHandle } from '@ipcManager/index'
import { webContentSend } from './web-content-send'
import AudioRecorder from '../utils/AudioRecorder'
import mainInit from './window-manager'
import SQLiteDB from '../../database/db'
import { Readable } from 'stream'
import { exec } from 'child_process'
const ffmpeg = require('fluent-ffmpeg')
const projectRoot = path.join(__dirname, "..")
if (process.platform === 'darwin') {
  ffmpeg.setFfmpegPath(path.join(projectRoot, 'resources/ffmpeg/mac/ffmpeg'))
} else if (process.platform === 'win32') {
  ffmpeg.setFfmpegPath(path.join(projectRoot, 'resources/ffmpeg/win/ffmpeg.exe'))
}

export class IpcMainHandleClass implements IIpcMainHandle {
  private allUpdater: Update
  private workbenchWin: BrowserWindow | null
  private floatWin: BrowserWindow | null
  constructor() {
    this.allUpdater = new Update()
    this.workbenchWin = null
    this.floatWin = null 
  }
  StartDownload: (
    event: Electron.IpcMainInvokeEvent,
    args: string,
  ) => void | Promise<void> = (event, downloadUrl) => {
    const windwos = BrowserWindow.fromWebContents(event.sender)
    if (!windwos) return
    new DownloadFile(windwos, downloadUrl).start()
  }
  StartServer: (
    event: Electron.IpcMainInvokeEvent,
  ) => string | Promise<string> = async () => {
    dialog.showErrorBox('error', 'API is obsolete')
    return 'API is obsolete'
  }
  StopServer: (event: Electron.IpcMainInvokeEvent) => string | Promise<string> =
    async () => {
      dialog.showErrorBox('error', 'API is obsolete')
      return 'API is obsolete'
    }
  HotUpdate: (event: Electron.IpcMainInvokeEvent) => void | Promise<void> = (
    event,
  ) => {
    const windows = BrowserWindow.fromWebContents(event.sender)
    if (!windows) return
    updater(windows)
  }
  OpenWin: (
    event: Electron.IpcMainInvokeEvent,
    args: { url: string; IsPay?: boolean; PayUrl?: string; sendData?: unknown; IsAudio?: boolean },
  ) => void | Promise<void> = (event, arg) => {
    let workbenchWin = this.workbenchWin = new BrowserWindow({
      titleBarStyle: process.platform == 'darwin' ? 'hidden' : 'default',
      width: 1300,
      height: 700,
      show: false,
      frame: true,
      autoHideMenuBar: true,
      fullscreenable: true,
      resizable: false,
      webPreferences: {
        sandbox: false,
        webSecurity: false,
        // 如果是开发模式可以使用devTools
        devTools: process.env.NODE_ENV === 'development' ? true : false,
        // 在macos中启用橡皮动画
        scrollBounce: process.platform === 'darwin',
        preload: getPreloadFile('main-preload'),
      },
    })
    // 开发模式下自动开启devtools
    if (process.env.NODE_ENV === 'development') {
      workbenchWin.webContents.openDevTools({ mode: 'undocked', activate: true })
    }
    if (arg.url.indexOf('http') != -1) {
      workbenchWin.loadURL(arg.url)
    } else {
      workbenchWin.loadURL(winURL + `#${arg.url}`)
    }
    workbenchWin.once('ready-to-show', () => {
      workbenchWin.show()
      if (arg.IsPay) {
        // 检查支付时候自动关闭小窗口
        const testUrl = setInterval(() => {
          const Url = workbenchWin.webContents.getURL()
          if (arg.PayUrl && Url.includes(arg.PayUrl)) {
            workbenchWin.close()
          }
        }, 1200)
        workbenchWin.on('close', () => {
          clearInterval(testUrl)
        })
      }
      if (arg.IsAudio) {
        AudioRecorder.init()
      }
    })
    // 渲染进程显示时触发
    workbenchWin.once('show', () => {
      webContentSend.SendDataTest(workbenchWin.webContents, arg.sendData)
    })
    workbenchWin.on('close', () => {
      this.workbenchWin = null
      if(this.floatWin){
        this.floatWin.close()
        this.floatWin = null
      }
    })
  }

  IsUseSysTitle: (
    event: Electron.IpcMainInvokeEvent,
  ) => boolean | Promise<boolean> = async () => {
    return config.IsUseSysTitle
  }
  AppClose: (event: Electron.IpcMainInvokeEvent) => void | Promise<void> = (
    event,
  ) => {
    SQLiteDB.close()
    app.quit()
  }
  CheckUpdate: (event: Electron.IpcMainInvokeEvent) => void | Promise<void> = (
    event,
  ) => {
    const windows = BrowserWindow.fromWebContents(event.sender)
    if (!windows) return
    this.allUpdater.checkUpdate(windows)
  }
  ConfirmUpdate: (event: Electron.IpcMainInvokeEvent) => void | Promise<void> =
    () => {
      this.allUpdater.quitAndInstall()
    }
  OpenMessagebox: (
    event: Electron.IpcMainInvokeEvent,
    args: Electron.MessageBoxOptions,
  ) =>
    | Electron.MessageBoxReturnValue
    | Promise<Electron.MessageBoxReturnValue> = async (event, arg) => {
      const res = await dialog.showMessageBox(
        BrowserWindow.fromWebContents(event.sender),
        {
          type: arg.type || 'info',
          title: arg.title || '',
          buttons: arg.buttons || [],
          message: arg.message || '',
          noLink: arg.noLink || true,
        },
      )
      return res
    }
  OpenErrorbox: (
    event: Electron.IpcMainInvokeEvent,
    arg: { title: string; message: string },
  ) => void | Promise<void> = (event, arg) => {
    dialog.showErrorBox(arg.title, arg.message)
  }
  InAppPurchase: (
    event: Electron.IpcMainInvokeEvent,
    productId: string
  ) => Promise<{ success: boolean; message: string }> = async (event, productId) => {
    try {
      // 1. 检查内购功能是否可用
      if (!inAppPurchase.canMakePayments()) {
        return { success: false, message: '当前设备不支持应用内购买' }
      }

      // 2. 设置交易监听器
      inAppPurchase.on('transactions-updated', (e, transactions) => {
        if (!Array.isArray(transactions)) return;

        transactions.forEach(transaction => {
          const payment = transaction.payment;
          const senderWindow = BrowserWindow.fromWebContents(event.sender)
          switch (transaction.transactionState) {
            case 'purchasing':
              console.log(`正在购买: ${payment.productIdentifier}`)
              senderWindow?.webContents.send('purchaseStatus', {
                status: 'purchasing',
                productId: payment.productIdentifier,
                msg: '正在购买，请稍候...',
                transaction,
              })
              break;
            case 'purchased':
              console.log(`购买成功: ${payment.productIdentifier}`);
              // 获取收据并验证
              const receiptURL = inAppPurchase.getReceiptURL();
              console.log('收据路径:', receiptURL);
              // 这里应该添加收据验证逻辑
              inAppPurchase.finishTransactionByDate(transaction.transactionDate);
              senderWindow?.webContents.send('purchaseStatus', {
                status: 'purchased',
                productId: payment.productIdentifier,
                msg: '购买成功',
                receiptURL,
                transaction,
              });
              break;

            case 'failed':
              console.log(`购买失败: ${payment.productIdentifier}`);
              inAppPurchase.finishTransactionByDate(transaction.transactionDate);
              senderWindow?.webContents.send('purchaseStatus', {
                status: 'failed',
                productId: payment.productIdentifier,
                msg: '购买失败',
                transaction,
              });
              break;

            case 'restored':
              console.log(`已恢复购买: ${payment.productIdentifier}`);
              senderWindow?.webContents.send('purchaseStatus', {
                status: 'restored',
                productId: payment.productIdentifier,
                msg: '已恢复购买',
                transaction,
              });
              break;
          }
        });
      });
      console.log('入参：', productId)
      // 3. 获取产品信息
      const products = await inAppPurchase.getProducts([productId]);
      console.log('产品信息:', products, '入参：', [productId]);
      if (!products || products.length === 0) {
        return { success: false, message: '无效的产品ID' };
      }

      // 4. 发起购买
      const purchaseResult = await inAppPurchase.purchaseProduct(productId);

      return {
        success: purchaseResult,
        message: purchaseResult ? '购买请求已提交' : '购买请求失败',
      };

    } catch (error:any) {
      console.error('内购出错:', error);
      return {
        success: false,
        message: `购买过程中出错: ${error.message}`
      };
    }
  }

  GetMediaAccessStatus: (event: Electron.IpcMainInvokeEvent) => boolean | Promise<boolean> = async () => {
    const status = systemPreferences.getMediaAccessStatus('microphone')
    if (status !== 'granted') {
      const result = await systemPreferences.askForMediaAccess('microphone')
      return result // true/false
    }
    return true // 已授权
  }

  CloseMainWindow: (event: Electron.IpcMainInvokeEvent) => void | Promise<void> = (
    event,
  ) => {
    mainInit.mainWindow?.close();
  }
  OpenSystemSettings: (event: Electron.IpcMainInvokeEvent) => void | Promise<void> = async () => {
    if (process.platform === 'darwin') {
      // macOS: 打开系统设置主界面
      shell.openExternal('x-apple.systempreferences:com.apple.preference.security?Privacy_Microphone')
    } else if (process.platform === 'win32') {
      // Windows: 打开麦克风隐私设置
      shell.openExternal('ms-settings:privacy-microphone')
    } else {
      // 其他平台可根据需要扩展
    }
  }
  // 创建用户的数据文件夹
  CreateUserFolder: (
    event: Electron.IpcMainInvokeEvent,
    u_s_id: string
  ) => Promise<any> = async (event,u_s_id) => {
    try {
      const userDataPath = app.getPath('userData')
      const userFilePath = path.join(userDataPath, 'UserFile')
      // 确保 userData 文件夹存在
      if (!fs.existsSync(userFilePath)) {
        fs.mkdirSync(userFilePath, { recursive: true })
      }
      // 用户ID文件夹路径
      const userFolder = path.join(userFilePath, u_s_id)
      // 如果用户ID文件夹已存在，直接返回
      if (fs.existsSync(userFolder)) {
        return { success: true, path: userFolder, message: '用户文件夹已存在' }
      }
      // 不存在则新建
      fs.mkdirSync(userFolder)
      return { success: true, message: '用户文件夹创建成功', path: userFolder }
    } catch (e: any) {
      return { success: false, message: e.message }
    }
  }
  DownloadOssFile: (
    event: Electron.IpcMainInvokeEvent, 
    args: {url: string, defaultName?: string}
  ) => Promise<any> = async (event, args) => {
    try {
      // 1. 让用户选择保存路径
      const browserWindow = BrowserWindow.fromWebContents(event.sender)
      if (!browserWindow) {
        return { success: false, message: '无法获取窗口实例' }
      }
      const saveDialogResult = await dialog.showSaveDialog(browserWindow, {
        title: '保存文件',
        defaultPath: args.defaultName || 'downloaded_file',
        buttonLabel: '保存',
      })
      if (saveDialogResult.canceled || !saveDialogResult.filePath) {
        return { success: false, message: '用户取消了保存' }
      }
      const filePath = saveDialogResult.filePath
      // 2. 下载文件到用户选择的路径
      return await new Promise((resolve) => {
        const file = fs.createWriteStream(filePath)
        const client = args.url.startsWith('https') ? https : http
        client.get(args.url, (response) => {
          if (response.statusCode !== 200) {
            resolve({ success: false, message: '下载失败，状态码：' + response.statusCode })
            return
          }
          response.pipe(file)
          file.on('finish', () => {
            file.close()
            resolve({ success: true, filePath })
          })
        }).on('error', (err) => {
          fs.unlink(filePath, () => {})
          resolve({ success: false, message: err.message })
        })
      })
    } catch (e: any) {
      return { success: false, message: e.message }
    }
  }
  OpenTheFolder: (
    event: Electron.IpcMainInvokeEvent, 
    args: {u_s_id: string, filePath: string}
  ) => Promise<any> = async (event, {u_s_id = '', filePath = ''}) => {
    try {
      let theFolderPath = ''
      if(filePath){
        theFolderPath = filePath
      }else{
        const userDataPath = app.getPath('userData')
        theFolderPath = path.join(userDataPath,'UserFile', u_s_id)
      }
      // 如果文件夹不存在 抛出错误
      if (!fs.existsSync(theFolderPath)) {
        return { success: false, message: '文件夹不存在或已删除' }
      }
      // 打开文件夹
      await shell.openPath(theFolderPath)
      return { success: true }
    } catch (e: any) {
      return { success: false, message: e.message }
    }
  }
  DownloadBlobFile: (
    event: Electron.IpcMainInvokeEvent,
    args: {file: ArrayBuffer, fileName: string, u_s_id: string, filePathStr: string}
  ) => Promise<any> = async (event, args) => {
    function transcodeToMp3(buffer:any){
      return new Promise((resolve, reject) => {
        const inputStream = new Readable()
        inputStream.push(buffer)
        inputStream.push(null)
        const chunks: Buffer[] = []
        ffmpeg(inputStream)
          .format('mp3')
          .on('error', reject)
          .on('end', () => resolve(Buffer.concat(chunks)))
          .pipe()
          .on('data', (chunk: Buffer) => chunks.push(chunk))
      })
    }
    try {
      const now = new Date()
      const dateStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`
      const userDataPath = app.getPath('userData')
      const folderPath = path.join(userDataPath, 'UserFile', args.u_s_id, dateStr, args.filePathStr)
      // 确保 userData/用户ID 文件夹存在
      if (!fs.existsSync(folderPath)) {
        fs.mkdirSync(folderPath, { recursive: true })
      }
      const fileName = args.fileName || 'null'
      const filePath = path.join(folderPath, fileName)
      if(args.fileName.indexOf('.mp3') != -1){
        const buffer = Buffer.from(args.file)
        const mp3Buffer = await transcodeToMp3(buffer)
        fs.writeFileSync(filePath, mp3Buffer as any)
      }else{
        fs.writeFileSync(filePath, Buffer.from(args.file))
      }
      // 写入文件
      // fs.writeFileSync(filePath, Buffer.from(args.file))
      return { success: true, filePath, folderPath }
    } catch (e: any) {
      return { success: false, message: e.message }
    }
  }
  GetFilesInFolder: (
    event: Electron.IpcMainInvokeEvent,
    args: {u_s_id: string, cloud: boolean}
  ) => Promise<any> = async (event, args) => {
    try {
      let {u_s_id, cloud} = args
      let folderPath = ''
      if(cloud){
        folderPath = path.join(app.getPath('userData'), 'UserFile', `.${u_s_id}_cloud_data`)
      }else{
        folderPath = path.join(app.getPath('userData'), 'UserFile', u_s_id)
      }
      if (!fs.existsSync(folderPath)) {
        return { success: false, message: '文件夹不存在' }
      }
      const fileNames = await fs.promises.readdir(folderPath)
      const files = await Promise.all(
        fileNames.map(async (name) => {
          const fullPath = path.join(folderPath, name)
          const stat = await fs.promises.stat(fullPath)
          return {
            name,
            path: fullPath,
            size: stat.size,
            ctime: stat.ctime, // 创建时间
            mtime: stat.mtime, // 修改时间
            isDirectory: stat.isDirectory(),
            isFile: stat.isFile(),
            type: stat.isDirectory() ? 'directory' : 'file'
          }
        })
      )
      return { success: true, files }
    } catch (e: any) {
      return { success: false, message: e.message }
    }
  }
  // 添加记录
  AddRecord: (
    event: Electron.IpcMainInvokeEvent,
    args: { folder_path: string, audio_path: string, txt_path: string, txt_timestamp_path: string, origin_file_name: string, file_name: string, duration: number, type: string, created_at: string }
  ) => Promise<any> = async (event, arg) => {
    try {
      SQLiteDB.addRecord(arg)
      return { success: true, message: '记录添加成功' }
    } catch (error: any) {
      return { success: false, message: error.message }
    }
  }
  // 获取记录
  GetHistory: (
    event: Electron.IpcMainInvokeEvent,
    args: {page: number, pageSize: number, fileName: string, type: string}
  ) => Promise<any> = async (event, arg) => {
    try {
      const records = SQLiteDB.getRecords({page: arg.page, pageSize: arg.pageSize, fileName: arg.fileName, type: arg.type})
      return { success: true, records }
    } catch (error: any) {
      return { success: false, message: error.message }
    }
  }
  UpdateFileName: (
    event: Electron.IpcMainInvokeEvent,
    args: { id: number, newFileName: string }
  ) => Promise<any> = async (event, arg) => {
    try {
      const result = SQLiteDB.updateFileName(arg)
      if(result.success){
        return { success: true, message: '文件名更新成功' }
      }else{
        return { success: false, message: result.message }
      }
    } catch (error: any) {
      return { success: false, message: error.message }
    }
  }
  // 更新上传状态
  UpdateUploadStatus: (
    event: Electron.IpcMainInvokeEvent,
    args: { id: number, isUploaded: boolean }
  ) => Promise<any> = async (event, arg) => {
    try {
      const result = SQLiteDB.updateUploadStatus(arg)
      if(result.success){
        return { success: true, message: '上传状态更新成功' }
      }else{
        return { success: false, message: result.message }
      }
    } catch (error: any) {
      return { success: false, message: error.message }
    }
  }

  // 批量更新上传状态
  BatchUpdateUploadStatus: (
    event: Electron.IpcMainInvokeEvent,
    args: { ids: number[], isUploaded: boolean }
  ) => Promise<any> = async (event, arg) => {
    try {
      const result = SQLiteDB.batchUpdateUploadStatus(arg)
      if(result.success){
        return { success: true, message: '批量更新上传状态成功' }
      }else{
        return { success: false, message: result.message }
      }
    } catch (error: any) {
      return { success: false, message: error.message }
    }
  }
  // 删除记录
  DeleteRecord: (
    event: Electron.IpcMainInvokeEvent,
    args: { recordItem: any, type: string}
  ) => Promise<any> = async (event, args) => {
    try {
      let {recordItem,type} = args
      if(type == 'local'){
        let result = SQLiteDB.deleteRecord(recordItem.id)
        // 递归删除本地文件夹及其内容
        if (recordItem.folder_path && fs.existsSync(recordItem.folder_path)) {
          fs.rmSync(recordItem.folder_path, { recursive: true, force: true })
        }else{
          return { success: true, message: '数据库记录删除成功，本地文件夹不存在' }
        }
        if(result.success){
          return { success: true, message: '记录删除成功' }
        }else{
          return { success: false, message: result.message }
        }
      }else{
        let {cloud_id,u_s_id} = recordItem
        const userDataPath = app.getPath('userData')
        const tempPath = path.join(userDataPath, 'UserFile', `.${u_s_id}_cloud_data`,`.${cloud_id}`)
        if (tempPath && fs.existsSync(tempPath)) {
          fs.rmSync(tempPath, { recursive: true, force: true })
          return { success: true, message: '本地云端数据删除成功' }
        }else{
          return { success: true, message: '本地文件夹不存在', path: tempPath }
        }
      }
    } catch (error: any) {
      return { success: false, message: error.message }
    }
  }
  
  ReadFile: (
    event: Electron.IpcMainInvokeEvent,
    args: { filePath: string,type: string, audioFormat?: string }
  ) => Promise<any> = async (event, arg) => {
    let {type,filePath,audioFormat} = arg
    let content = null
    if(type == 'audio'){
      const buffer = fs.readFileSync(filePath)
      if(audioFormat == 'buffer'){
        content = buffer
      }else{
        const mimeType = 'audio/mp3'
        const base64 = buffer.toString('base64')
        content = `data:${mimeType};base64,${base64}`
      }
    }else if(type == 'txt'){
      content = fs.readFileSync(filePath, 'utf-8');
    }else if(type == 'receipt'){
      let receiptData = fs.readFileSync(filePath)
      content = receiptData.toString('base64')
    }
    return { success: true, content }
  }

  DownloadFileAsZip: (
    event: Electron.IpcMainInvokeEvent,
    files: { name: string, url?: string, base64?: string }[]
  ) => Promise<any> = async (event, files) => {
    const zip = new JSZip()
    for (const file of files) {
      let buffer: Buffer | null = null
      try {
        if (file.url) {
          // 下载 OSS 链接
          const res = await fetch(file.url)
          if (!res.ok) throw new Error(`下载失败: ${file.url}`)
          buffer = Buffer.from(await res.arrayBuffer())
        } else if (file.base64) {
          // base64 转 Buffer
          const base64Data = file.base64.replace(/^data:.*?base64,/, '')
          buffer = Buffer.from(base64Data, 'base64')
        }
        if (buffer) {
          // 保证文件名有扩展名
          zip.file(file.name, buffer)
        }
      } catch (e) {
        // 某个文件失败，继续下一个
        console.error(`文件处理失败: ${file.name}`, e)
      }
    }

    // 没有可用文件
    if (Object.keys(zip.files).length === 0) {
      return { success: false, message: '没有可用文件可打包' }
    }

    // 生成 zip 文件
    const zipContent = await zip.generateAsync({ type: 'nodebuffer' })

    const browserWindow = BrowserWindow.fromWebContents(event.sender)
    if (!browserWindow) {
      return { success: false, message: '无法获取窗口实例' }
    }
    let zipFileName = files[0].name.split('.')[0] + '.zip'
    // 让用户选择保存路径
    const { filePath, canceled } = await dialog.showSaveDialog(browserWindow, {
      title: '保存压缩包',
      defaultPath: zipFileName,
      buttonLabel: '保存',
      filters: [{ name: 'Zip Files', extensions: ['zip'] }]
    })
    if (canceled || !filePath) return { success: false, message: '用户取消保存' }

    // 写入 zip 文件
    fs.writeFileSync(filePath, zipContent)
    return { success: true, filePath }
  }
  DownloadFilesToHiddenFolder: (
    event: Electron.IpcMainInvokeEvent,
    args: { 
      files: { name: string, url?: string, base64?: string }[],
      u_s_id: string,
      cloud_id: string
    }
  ) => Promise<any> = async (event, args) => {
    try {
      let { files, u_s_id, cloud_id } = args
      let newTagetFolder = ''
      // 1. 处理隐藏文件夹名
      if (process.platform === 'win32') {
        // Windows: 文件夹名不变，后续用 attrib 设置隐藏
      } else {
        // macOS/Linux: 文件夹名以 . 开头
        const userDataPath = app.getPath('userData')
        newTagetFolder = path.join(userDataPath, 'UserFile', `.${u_s_id}_cloud_data`,cloud_id)
        const parent = path.dirname(newTagetFolder)
        const base = path.basename(newTagetFolder)
        if (!base.startsWith('.')) {
          newTagetFolder = path.join(parent, '.' + base)
        }
      }

      // 2. 检查/创建目标文件夹
      if (!fs.existsSync(newTagetFolder)) {
        fs.mkdirSync(newTagetFolder, { recursive: true })
      }

      // 3. 处理每个文件
      for (const file of files) {
        let buffer: Buffer | null = null
        if(file.url){
          const res = await fetch(file.url)
          if (!res.ok) throw new Error(`下载失败: ${file.url}`)
          buffer = Buffer.from(await res.arrayBuffer())
        }else if(file.base64){
          const base64Data = file.base64.replace(/^data:.*?base64,/, '')
          buffer = Buffer.from(base64Data, 'base64')
        }
        if (buffer) {
          const filePath = path.join(newTagetFolder, file.name)
          fs.writeFileSync(filePath, buffer)
        }
      }
      // 4. 设置隐藏属性（Windows）
      if (process.platform === 'win32') {
        exec(`attrib +h "${newTagetFolder}"`, (err) => {
          if (err) {
            console.warn('设置隐藏属性失败:', err.message)
          }
        })
      }

      return { success: true, message: '文件全部保存成功', folder: newTagetFolder }
    } catch (e: any) {
      return { success: false, message: e.message }
    }
  }
  GetEnvInfo: (
    event: Electron.IpcMainInvokeEvent,
  ) => Promise<any> = async (event) => {
    if(process.env.NODE_ENV == 'development'){
      return { 
        success: true, 
        data: {
          apiBaseUrl: 'https://www.peytonpeng.top',
          downloadBaseUrl: 'https://www.peytonpeng.top/jifanyi/download',
        } 
      }
    }else{
      return { 
        success: true, 
        data: {
          apiBaseUrl: 'https://www.yffsh.com',
          downloadBaseUrl: 'https://www.yffsh.com/jifanyi/download',
        } 
      }
    }
  }
  OpenFloatWin: (
    event: Electron.IpcMainInvokeEvent,
    args: { url: string },
  ) => Promise<any> = (event, arg) => {
    return new Promise((resolve, reject) => {
      if(this.floatWin && !this.floatWin.isDestroyed()){
        this.floatWin.show()
        resolve({ success: true, message: '窗口打开成功' })
      }
      const floatWin = this.floatWin = new BrowserWindow({
        titleBarStyle: process.platform == 'darwin' ? 'default' : 'hidden',
        width: 1300,
        height: 300,
        minWidth: 920,
        minHeight: 200,
        show: false,
        frame: false,
        autoHideMenuBar: false,
        fullscreenable: false,
        transparent: true,
        // alwaysOnTop: true,
        resizable: true,
        webPreferences: {
          sandbox: false,
          webSecurity: false,
          devTools: process.env.NODE_ENV === 'development' ? true : false,
          scrollBounce: process.platform === 'darwin',
          preload: getPreloadFile('main-preload'),
        },
      });
  
      floatWin.loadURL(winURL + `#${arg.url}`);
  
      if (process.env.NODE_ENV === 'development') {
        floatWin.webContents.openDevTools({ mode: 'undocked', activate: true });
      }
  
      floatWin.once('ready-to-show', () => {
        floatWin.show();
        this.workbenchWin.minimize();
      });
      floatWin.webContents.once('did-finish-load', () => {
        resolve({ success: true, message: '窗口打开成功' }); // 通知渲染进程窗口已打开
      })
  
      floatWin.on('closed', () => {
        // 清理引用等
        this.floatWin = null
      });
  
      // 可选：超时/异常处理
      floatWin.on('unresponsive', () => {
        reject(new Error('floatWin unresponsive'));
      });
    });
  };
  CloseFloatWin: (event: Electron.IpcMainInvokeEvent) => void | Promise<void> = (event) => {
    if(this.floatWin && !this.floatWin.isDestroyed()){
      this.floatWin.close()
      this.floatWin = null
      this.workbenchWin.show()
      this.workbenchWin.webContents.send('SetFloatWinSwitchStatus', false)
      // 如果工作台窗口不存在
      // if(this.workbenchWin && !this.workbenchWin.isDestroyed()){
      //   this.workbenchWin.show()
      //   this.workbenchWin.webContents.send('SetFloatWinSwitchStatus', false)
      // }else{
      //   this.OpenWin('',{url: '/main'})
      // }
    }
  }
  WorkbenchWinToFloatWin: (event: Electron.IpcMainInvokeEvent, args: any) => void | Promise<void> = (event, arg) => {
    if (this.floatWin  && !this.floatWin.isDestroyed()) {
      this.floatWin.webContents.send('WorkbenchToFloat', arg);
    }
  }
  SetFloatWinAlwaysOnTop: (event: Electron.IpcMainInvokeEvent, args: Boolean) => void | Promise<void> = (event, arg) => {
    if (this.floatWin  && !this.floatWin.isDestroyed()) {
      this.floatWin.setAlwaysOnTop(arg)
    }
  }
}
