const { ipcMain } = require("electron")
import FFmpegRecord from "./FFmpegRecord"
export default class AudioRecorder {

  /**
   * 初始化录音模块
   */
  static init() {
    ipcMain.handle("start-audio-recording", async (event:any) => {
      if (FFmpegRecord.recording) return event.sender.send("start-audio-recording", { success: false, err: '录音中' })
      FFmpegRecord.startRecording((res:any) => {
        event.sender.send("start-audio-recording", res)
      })
    })
    ipcMain.handle("stop-audio-recording", async (event:any) => {
      FFmpegRecord.stopRecording()
      event.sender.send("stop-audio-recording")
    })
  }
}
