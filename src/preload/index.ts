import { contextBridge, ipc<PERSON>enderer, shell } from 'electron'
import { platform, release, arch, networkInterfaces } from 'os'
import { IpcChannelMainClass, IpcChannelRendererClass } from '@ipcManager/index'

function getIpcRenderer() {
  const IpcRenderer: Record<string, any> = {}
  Object.keys(new IpcChannelMainClass()).forEach((channel) => {
    IpcRenderer[channel] = {
      invoke: async (args: any) => ipcRenderer.invoke(channel, args),
    }
  })
  Object.keys(new IpcChannelRendererClass()).forEach((channel) => {
    IpcRenderer[channel] = {
      on: (listener: (...args: any[]) => void) => {
        ipcRenderer.removeListener(channel, listener)
        ipcRenderer.on(channel, listener)
      },
      once: (listener: (...args: any[]) => void) => {
        ipcRenderer.removeListener(channel, listener)
        ipcRenderer.once(channel, listener)
      },
      removeAllListeners: () => ipcRenderer.removeAllListeners(channel),
    }
  })
  return IpcRenderer
}

contextBridge.exposeInMainWorld('ipcRendererChannel', getIpcRenderer())

contextBridge.exposeInMainWorld('systemInfo', {
  platform: platform(),
  release: release(),
  arch: arch(),
  osVersion: process.getSystemVersion(),
  ip: getIpAddress()
})
function getIpAddress() {
  const interfaces = networkInterfaces()
  for (const interfaceName in interfaces) {
    const iface = interfaces[interfaceName];
    for (const alias of iface || []) {
      if (alias.family === 'IPv4' && !alias.internal) {
        return alias.address;
      }
    }
  }
  return '127.0.0.1'
}
contextBridge.exposeInMainWorld('shell', shell)

contextBridge.exposeInMainWorld('crash', {
  start: () => {
    process.crash()
  },
})

contextBridge.exposeInMainWorld('audioAPI', {
  startRecording: () => ipcRenderer.invoke('start-audio-recording'),
  stopRecording: () => ipcRenderer.invoke('stop-audio-recording'),

  onStartRecording: (callback:any) => ipcRenderer.on('start-audio-recording', callback),
  onStopRecording: (callback:any) => ipcRenderer.on('stop-audio-recording', callback),

  removeAllListeners: (channel:any) => ipcRenderer.removeAllListeners(channel)
});

// contextBridge.exposeInMainWorld('electronAPI', {
//   requestMicrophonePermission: () => ipcRenderer.invoke('getMediaAccessStatus')
// })


