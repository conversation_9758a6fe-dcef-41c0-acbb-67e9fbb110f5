import request from '@renderer/utils/request'
export const getLoginQrcode = (data) => {
  return request.get('/jifanyi/user/generate_qr',data)
}
export const loopGetLoginStatus = (data) => {
  return request.post('/jifanyi/user/poll_qr_status',data)
}
export const login = (data) => {
  return request.post('/jifanyi/user/login',data)
}
export const getTelCode = (data) => {
  return request.get('/jifanyi/user/send_sms/',data)
}
export const getEmailCode = (data) => {
  return request.get('/jifanyi/user/send_email/',data)
}
export const getUserAgreement = (data) => {
  return request.get('/jifanyi/agreement/user',data)
}
export const getPrivacyAgreement = (data) => {
  return request.get('/jifanyi/agreement/privacy',data)
}
export const getHomeBanner = (data) => {
  return request.get('/jifanyi/banner',data)
}
export const createOrder = (data) => {
  return request.post('/jifanyi/create_order',data)
}
export const toPay = (data) => {
  return request.post('/jifanyi/alipay',data)
}
// 获取消费记录
export const getUseHistory = (data) => {
  return request.get('/jifanyi/consumption_record_list/',data)
}
// 获取充值记录
export const getRechargeHistory = (data) => {
  return request.get('/jifanyi/purchase_history_list/',data)
}
// 获取全部充值商品
export const getGoods = (data) => {
  return request.get('/jifanyi/shops',data)
}
// 获取资费标准 文档/音频/图片
export const getTariffStandard = (data) => {
  return request.get('/jifanyi/billing_list',data)
}
// 图片翻译
export const translateImage = (data) => {
  return request.uploadFile('/jifanyi/translate_image',data)
}
// 获取图片翻译结果
export const getImgTranslationRes = (data) => {
  return request.post('/jifanyi/get_translation_image',data)
}
// 获取订单详情/支付状态
export const getOrderDetail = (data) => {
  return request.get('/jifanyi/order_detail',data)
}
// 获取用户信息
export const getUserInfo = (data) => {
  return request.get('/jifanyi/user/user_info',data)
}
// 文档翻译描述
export const getDocsTranslateDesc = (data) => {
  return request.get('/jifanyi/get_translate_desc',data)
}
// 音频转写描述
export const getAudioTranslateDesc = (data) => {
  return request.get('/jifanyi/get_translate_audio_desc',data)
}
// 文档上传翻译
export const uploadTranslateDocs = (data) => {
  return request.uploadFile('/jifanyi/upload_translate_doc',data)
}
// 文档上传翻译  开始翻译
export const translateDocs = (data) => {
  return request.post('/jifanyi/translation_doc',data)
}
// 获取文档翻译结果
export const getDocsTranslationRes = (data) => {
  return request.post('/jifanyi/get_translation_doc',data)
}
// 音频文件上传
export const uploadTranslateAudio = (data) => {
  return request.uploadFile('/jifanyi/upload_audio',data)
}
// 音频 开始转写
export const translateAudio = (data) => {
  return request.post('/jifanyi/translation_audio',data)
}
// 获取音频转写结果
export const getAudioTranslationRes = (data) => {
  return request.post('/jifanyi/get_translation_audio',data)
}
// 文本翻译
export const translateText = (data) => {
  return request.post('/jifanyi/translate_txt',data)
}
// 获取图片翻译支持的语言
export const getImgTranslateSupportLang = (data) => {
  return request.get('/jifanyi/image_languages',data)
}
// 获取文档翻译支持的语言
export const getDocsTranslateSupportLang = (data) => {
  return request.get('/jifanyi/document_languages',data)
}
// 获取音频转写支持的语言
export const getAudioTranslateSupportLang = (data) => {
  return request.get('/jifanyi/audio_languages',data)
}
// 历史记录
export const getTranslateHistory = (data) => {
  return request.get('/jifanyi/get_translation_history',data)
}
// 历史记录-详情
export const getHistoryDetail = (data) => {
  return request.post('/jifanyi/translation_history/detail',data)
}
// 客服信息（联系方式）
export const getContactInfo = (data) => {
  return request.get('/jifanyi/contact_info',data)
}
// 提交反馈
export const saveFeedback = (data) => {
  return request.uploadFile('/jifanyi/feedback',data)
}
// 文本翻译支持的语言
export const getTextTranslateSupportLang = (data) => {
  return request.get('/jifanyi/text_languages',data)
}
// 获取AZ的key
export const getAzKey = (data) => {
  return request.get('/jifanyi/az_key',data)
}
// 同传开始计费
export const startDeduct = (data) => {
  return request.post('/jifanyi/start_deduct_balance',data)
}
// 同传 运行中心跳接口
export const recordKeepUp = (data) => {
  return request.post('/jifanyi/keep_up',data)
}
// 同传结束计费
export const stopDeduct = (data) => {
  return request.post('/jifanyi/end_deduct_balance',data)
}
// 同传--上传文本和音频
export const tcUpload = (data) => {
  return request.uploadFile('/jifanyi/upload_simultaneous',data)
}
// 同传--支持的语言
export const getTcLang = (data) => {
  return request.get('/jifanyi/simultaneous_languages',data)
}
export const editUserInfo = (data) => {
  return request.uploadFile('/jifanyi/user/update_user_info',data)
}
export const checkBalance = (data) => {
  return request.post('/jifanyi/check_balance',data)
}
export const getBillingList = (data) => {
  return request.get('/jifanyi/billing_list',data)
}
export const getRechargeDesc = (data) => {
  return request.get('/jifanyi/recharge_notice',data)
}
// 兑换服务包
export const exchangeCode = (data) => {
  return request.post('/jifanyi/redeem',data)
}
// 同传--设备冲突时 强制开始
export const forceBegin = (data) => {
  return request.post('/jifanyi/take_over_session',data)
}
// 代理商服务 banner 列表
export const getSericeBanner = (data) => {
  return request.get('/jifanyi/activity_banners',data)
}
export const checkNewbieBenefitPop = (data) => {
  return request.get('/jifanyi/check_newbie_eligibility',data)
}
// 云存储上传
export const uploadFileForCloud = (data) => {
  return request.uploadFile('/jifanyi/storage/upload_files/',data)
}
// 云存储列表
export const getCloudFileList = (data) => {
  return request.get('/jifanyi/storage/list_files/',data)
}
// 云存储删除
export const deleteCloudFile = (data) => {
  return request.post('/jifanyi/storage/delete_collection',data)
}
// 云存储下载 获取文件url
export const getCloudFileUrl = (data) => {
  return request.get('/jifanyi/storage/download_files',data)
}
// 获取商品列表
export const getGoodsList = (data) => {
  return request.get('/jifanyi/shops/v13',data)
}
// 订阅会员
export const subscribeVip = (data) => {
  return request.post('/jifanyi/subscribe',data)
}
// 苹果支付验证票据
export const applePay = (data) => {
  return request.post('/jifanyi/apple_pay',data)
}
