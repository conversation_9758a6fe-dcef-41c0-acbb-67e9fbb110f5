import { RouteRecordRaw } from 'vue-router'
//import notFound from '@renderer/views/404.vue'
//import landingPage from '@renderer/views/landing-page/landing-page.vue'
import audioPage from '@renderer/views/landing-page/audio-page.vue'
const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'login',
    component: () => import('@renderer/views/login.vue'),
    meta: {
      title: '登录',
    },
  },
  {
    path: '/main',
    name: 'main',
    component: () => import('@renderer/views/main.vue'),
    meta: {
      title: '工作台',
    },
  },
  {
    path: '/floatWin',
    name: 'floatWin',
    component: () => import('@renderer/views/floatWin.vue'),
    meta: {
      title: '悬浮窗',
    },
  },
  // {
  //   path: '/',
  //   name: 'login',
  //   component: landingPage,
  //   meta: {
  //     title: '登录'
  //   }
  // },
  { path: '/audio', name: '录音功能测试', component: audioPage },
]

export default routes
