import CryptoJS from 'crypto-js'
export function decrypt(data) {
  let fakeKey  = 'U2FsdGVkX191RbLIlxnGh42RpxUbgNjxOGUnoFBWEkQ='
  let yffFront  = 'yffFront'
  const str = decryptAES(fakeKey,yffFront)
  // 定义密钥和 IV，确保它们是 16 字节
  const key = CryptoJS.enc.Utf8.parse(str.padEnd(16, '\0'));  // 填充至 16 字节
  const iv = CryptoJS.enc.Utf8.parse(str.padEnd(16, '\0'));   // 填充至 16 字节
  // 使用 AES 解密
  const decrypted = CryptoJS.AES.decrypt(data, key, { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 });

  // 将解密后的结果转为 UTF-8 字符串
  const decryptedData = decrypted.toString(CryptoJS.enc.Utf8);

  return decryptedData
}

const fakeKey = "aAB1AG8AegBOAG8AbgBnAA==";

// function encryptAES(plainText, secretKey) {
//   // 生成随机 IV（16字节）
//   const iv = CryptoJS.lib.WordArray.random(16);
  
//   // 加密
//   const encrypted = CryptoJS.AES.encrypt(plainText, secretKey, {
//     iv: iv,
//     mode: CryptoJS.mode.CBC,
//     padding: CryptoJS.pad.Pkcs7
//   });

//   // 返回 IV + 密文（Base64格式）
//   return iv.toString(CryptoJS.enc.Base64) + ":" + encrypted.toString();
// }
// console.log(encryptAES('huozhong','yffFront'),'******')
function decryptAES(encryptedData, secretKey) {
  const iv = '8UJ36kKXKl03ZFRBiTp4PA=='
  const ciphertext = encryptedData

  // 解密
  const decrypted = CryptoJS.AES.decrypt(ciphertext, secretKey, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  });

  return decrypted.toString(CryptoJS.enc.Utf8);
}
// let temp = '8UJ36kKXKl03ZFRBiTp4PA==:U2FsdGVkX191RbLIlxnGh42RpxUbgNjxOGUnoFBWEkQ='