import axios from 'axios'
import { ElMessage } from 'element-plus'
import { decrypt } from './decrypt.js'
import { getFingerprint } from './fingerprint.js'
import { getBrowserInfo,isObject } from './utils.js'
const { ipcRendererChannel } = window
const { data: { apiBaseUrl } } = await ipcRendererChannel.GetEnvInfo.invoke()
const baseURL = apiBaseUrl
const { systemInfo } = window
const service = axios.create({
  baseURL,
  timeout: 36000,
  headers: {
    'Content-Type': 'application/json',
    'Version': '1.3.0',
    'Channel-Type': '',  //window: 3   mac: 4
    'Device-Id': '',
    'System-Version': '',
    'IP-Address': ''
  }
})

//请求拦截器
service.interceptors.request.use(
  async config => {
    systemInfo.platform == 'win32' ? config.headers['Channel-Type'] = 3 : config.headers['Channel-Type'] = 4
    let fingerprint = await getFingerprint()
    config.headers['Device-Id'] = fingerprint || ''
    // let browserInfo = getBrowserInfo()
    config.headers['System-Version'] = systemInfo.osVersion
    config.headers['IP-Address'] = systemInfo.ip
    let token = localStorage.getItem('token')
    if (token) {
      config.headers.TOKEN = token
    }
    return config
  },
  error => {
    console.log(error)
    return Promise.reject(error)
  }
)
// 响应拦截器
service.interceptors.response.use(
  res => {
    res.data.data = JSON.parse(decrypt(res.data.data))
    const code = res.data.code
    switch(code){
      case 'YFF0':
      case 'YFF008':
      case 'YFF009':
        return Promise.resolve(res.data)
        break
      case 'YFF007':
        // ElMessage('正在翻译，稍后可在历史记录中查看/下载')
        return Promise.resolve(res.data)
        break
      case 'YFF-1':
        ElMessage.error(res.data.msg)
        return Promise.resolve(res.data)
        break
      case 'YFF009':
        ElMessage.error('翻译失败，可在历史记录查看失败原因')
        return Promise.resolve(res.data)
        break
      case 'YFF4014':
      case 'YFF005':
        let token = localStorage.getItem('token')
        if(!token){
          ElMessage.warning('请先登录')
          return
        }
        ElMessage.warning('请先登录')
        localStorage.clear()
        window.location.reload()
        return Promise.resolve(res.data)
        break
      default:
        ElMessage.error(res.data.msg)
        return Promise.resolve(res.data)
    }
    // if (res.data.code === 'YFF0') {
    //   return Promise.resolve(res.data)
    // } else {
    //   switch(res.data.code){
    //     case 'YFF4014':
    //       ElMessage.error('请先登录')
    //       break
    //     default:
    //       ElMessage.error('请求失败')
    //   }
    //   return Promise.resolve(res.data)
    // }
  },
  err => {
    if(err.name === 'CanceledError'){
      return
    }
    ElMessage.error(err)
    return Promise.resolve(err)
  }
)
class Request {
  get(url,params = {}){
    return service.get(url,{ params,signal: params.signal })
  }
  post(url,data = {}){
    return service.post(url,data)
  }
  uploadFile(url,data){
    const formData = new FormData()
    if(isObject(data)){
      Object.keys(data).forEach(key=>{
        const value = data[key]
        if(Array.isArray(value)){
          if (value.every((item) => item instanceof File)) {
            value.forEach((file) => {
              formData.append(key, file)
            })
          } else {
            // 普通数组，转换为 JSON 字符串后追加
            formData.append(key, value)
          }
        }else{
          formData.append(key,value)
        }
      })
    }
    return service.post(
      url,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
      }
    )
  }
}
export default new Request()
