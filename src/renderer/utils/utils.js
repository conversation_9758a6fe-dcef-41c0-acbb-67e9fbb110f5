export function getBrowserInfo() {
  const userAgent = navigator.userAgent;
  const platform = navigator.platform.toLowerCase();
  let name = ""
  let version = ""
  let os = ""
  
  // 检测浏览器类型及版本
  if (/firefox\/\d+/i.test(userAgent)) {
    name = "Firefox"
    version = userAgent.match(/firefox\/(\d+)/i)[1]
  } else if (/edg\/\d+/i.test(userAgent)) {
    name = "Edge"
    version = userAgent.match(/edg\/(\d+)/i)[1]
  } else if (/chrome\/\d+/i.test(userAgent)) {
    name = "Chrome"
    version = userAgent.match(/chrome\/(\d+)/i)[1]
  } else if (/safari\/\d+/i.test(userAgent) && !/chrome\/\d+/i.test(userAgent)) {
    name = "Safari"
    version = userAgent.match(/version\/(\d+)/i)[1]
  } else if (/opera|opr\/\d+/i.test(userAgent)) {
    name = "Opera"
    version = userAgent.match(/opera|opr\/(\d+)/i)[1]
  } else if (/msie \d+/i.test(userAgent) || /trident\/\d+/i.test(userAgent)) {
    name = "Internet Explorer"
    version = userAgent.match(/(?:msie |rv:)(\d+)/i)[1]
  }

  if (platform.includes("mac")) {
    os = "macOS";
  } else if (platform.includes("win")) {
    os = "Windows";
  } else if (platform.includes("linux")) {
    os = "Linux";
  } else if (/iphone|ipad|ipod/.test(platform)) {
    os = "iOS";
  } else if (/android/.test(platform)) {
    os = "Android";
  } else {
    os = "Unknown";
  }

  return { name, version, os }
}
export function isObject(val) {
  return Object.prototype.toString.call(val) === '[object Object]';
}
export function getFormat(val){
  const fileExtension = val.split('.').pop().toLowerCase();
  const fileTypes = {
    image: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
    audio: ['mp3', 'wav', 'ogg', 'aac', 'flac'],
    // document: ['txt', 'xls', 'xlsx'],
    txt: ['txt'],
    pdf: ['pdf'],
    word: ['doc', 'docx'],
    ppt: ['ppt', 'pptx'],
    xml: ['xml'],
  }
  for (const type in fileTypes) {
    if (fileTypes[type].includes(fileExtension)) {
      return type
    }
  }
  return 'unknown'
}

export function timeStringToSeconds(time) {
  const [hours, minutes, seconds] = time.split(':');
  return parseInt(hours) * 3600 + parseInt(minutes) * 60 + parseInt(seconds);
}
export function handleAudioText(content) {
  const subtitles = []
  const lines = content.split('\n') // 按行分割
  let currentSubtitle = { start: null, end: null, text: '',active: false }

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()

    // 跳过空行和非字幕内容
    if (!line || line.startsWith('WEBVTT') || line.startsWith('Kind') || line.startsWith('Language')) {
      continue;
    }
    // 如果是时间戳行，解析时间
    const timeRegex = /^(\d{2}:\d{2}:\d{2}\.\d{3}) --> (\d{2}:\d{2}:\d{2}\.\d{3})$/
    const match = line.match(timeRegex)
    if (match) {
      if (currentSubtitle.start !== null) {
        subtitles.push(currentSubtitle)
      }
      currentSubtitle = {
        start: timeStringToSeconds(match[1]),
        end: timeStringToSeconds(match[2]),
        text: ''
      };
    } else {
      // 累积字幕内容
      currentSubtitle.text += (currentSubtitle.text ? '\n' : '') + line
    }
  }
  // 将最后一个字幕添加到数组中
  if (currentSubtitle.start !== null) {
    subtitles.push(currentSubtitle)
  }
  return subtitles
}
export function formatTimestamp(ns){
  const totalMs = Math.floor(ns / 10000); // 100 纳秒转毫秒
  const hours = Math.floor(totalMs / 3600000);
  const minutes = Math.floor((totalMs % 3600000) / 60000);
  const seconds = Math.floor((totalMs % 60000) / 1000);
  const milliseconds = totalMs % 1000;
  return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(2, "0")}:${String(seconds).padStart(2, "0")}.${String(milliseconds).padStart(3, "0")}`;
}
export function checkEmail(email){
  if(!email){
    return false
  }
  if(email){
    let reg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
    if(!reg.test(email)){
      return false
    }
  }
  return true
}
export function checkMobile (mobile){
  if(!mobile){
    return false
  }
  if(mobile){
    let reg = /^1[3-9]\d{9}$/
    if(!reg.test(mobile)){
      return false
    }
  }
  return true
}
// 是否是移动端
// export function isMobile() {
//   const userAgent = navigator.userAgent || navigator.vendor || window.opera;
//   const isMobileDevice = /android|iPhone|iPod|iPad/i.test(userAgent);

//   return isMobileDevice || (window.outerWidth <= 800 && window.outerHeight <= 600);
// }
export function isMobile() {
  // 1. User-Agent检测（精确模式）
  const ua = navigator.userAgent || navigator.vendor || window.opera;
  // ✅ 匹配移动设备关键字（包含Android/iOS）
  const isMobileUA = /(?:Android|iPhone|iPad|iPod|BlackBerry|Opera Mini)/i.test(ua);
  // ❌ 排除平板设备（iPad/iPod）
  const isTabletUA = /(?:iPad|iPod)/i.test(ua);
  // 2. 视口尺寸检测（使用innerWidth更准确）
  const isSmallScreen = window.innerWidth <= 768; 
  // 3. 触摸事件检测（补充判断）
  const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  // 📱 综合判断：移动UA + 非平板 + 小屏 + 触摸支持
  return !(isTabletUA) && (isMobileUA || (isSmallScreen && hasTouch));
}
// 获取当前浏览器环境类型 安卓/IOS/微信/ios微信/安卓微信/小程序
export function getCurBrowserEnv() {
  const UA = navigator.userAgent
  // 判断是否是微信小程序
  if (typeof window.__wxjs_environment !== 'undefined') {
    if (window.__wxjs_environment === 'miniprogram') {
      return 'miniprogram'
    }
  }
  // 判断是否是微信内置浏览器
  if (/MicroMessenger/i.test(UA)) {
    if (/iPhone|iPad|iPod/i.test(UA)) {
        return 'wechat_ios'
    } else if (UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1) {
        return 'wechat_android'
    }
    return 'wechat'
}
  // 判断是否是 iOS
  if (/iPhone|iPad|iPod/i.test(UA)) {
    return 'ios'
  }
  // 判断是否是 Android
  if (UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1) {
    return 'android'
  }
  // 默认返回 unknown
  return 'unknown'
}
export function downloadFun(url, name = 'yff_download_file') {
  if(!url){
    ElMessage.error('下载地址为空')
    return
  }
  const apkUrl = url
  const link = document.createElement('a')
  link.href = apkUrl
  link.download = name
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
export function getServiceTypeFun(type) {
  switch (type) {
    case 'simultaneous_interpretation':
      return '同声传译'
    case 'document_translation':
      return '文档翻译'
    case 'audio_transcription':
      return '音频转写'
    case 'image_translation':
      return '图片翻译'
    case 'text_translation':
      return '文本翻译'
  }
}
export const formatTimeAsHMS = (sec) => {
  const h = String(Math.floor(sec / 3600)).padStart(2, '0')
  const m = String(Math.floor((sec % 3600) / 60)).padStart(2, '0')
  const s = String(sec % 60).padStart(2, '0')
  return `${h}:${m}:${s}`
}
