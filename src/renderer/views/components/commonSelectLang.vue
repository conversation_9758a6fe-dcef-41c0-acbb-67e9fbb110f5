<template>
  <transition-group name="fade">
    <div class="contact-book flex w-80%" v-if="visible">
      <!-- 左侧内容区 -->
      <div class="contact-list">
        <template v-for="letter in availableLetters" :key="letter">
          <div v-if="grouped[letter]?.length" class="letter-group">
            <div class="letter-header" :id="`group-${letter}`">{{ letter }}</div>
            <div class="flex flex-wrap">
              <div v-for="item in grouped[letter]" :key="item.id || item.value" class="contact-item w-max-content p-10px pt-6 pb-6 m-6px font-size-15px cursor-pointer rounded-6px" @click="handleSelect(item)">
                {{ item.label || item.name }}
              </div>
            </div>
          </div>
        </template>
      </div>
      <!-- 右侧字母索引 -->
      <div class="letter-index">
        <div v-for="letter in allLetters" :key="letter" :class="[ 'letter-index-item',
          {
            active: currentLetter === letter,
            disabled: !grouped[letter]?.length,
          },
        ]" @click="scrollToLetter(letter)">
          {{ letter }}
        </div>
      </div>
      <el-icon class="close-icon position-fixed top-10px right-50px cursor-pointer" size="20px" color="#cccccc" @click="closeFun"><CircleCloseFilled /></el-icon>
    </div>
    <div class="contact-book-bg w-100vw h-100vh position-fixed top-0 left-0 z-1 bg-#0003" v-if="visible" @click="closeFun"></div>
  </transition-group>
</template>

<script setup>
import { ref, computed } from 'vue'
import { pinyin } from 'pinyin-pro' // 需先 npm install pinyin-pro
import { CircleCloseFilled } from '@element-plus/icons-vue'
const props = defineProps({
  // 数据源，格式为 [{label: '中文/English', value: '唯一标识'}]
  dataSource: {
    type: Array,
    required: true,
  },
  visible: {
    type: Boolean,
    required: true,
  },
})
const emit = defineEmits(['select'])

const allLetters = [
  'A',
  'B',
  'C',
  'D',
  'E',
  'F',
  'G',
  'H',
  'I',
  'J',
  'K',
  'L',
  'M',
  'N',
  'O',
  'P',
  'Q',
  'R',
  'S',
  'T',
  'U',
  'V',
  'W',
  'X',
  'Y',
  'Z',
  '#',
]

// 按首字母分组
const grouped = computed(() => {
  const groups = {}
  allLetters.forEach((l) => (groups[l] = []))
  props.dataSource.forEach((item) => {
    let name = item.label || item.name || ''
    let first = name[0]
    let letter = ''
    if (/[a-zA-Z]/.test(first)) {
      letter = first.toUpperCase()
    } else {
      // 用 pinyin-pro 提取中文首字母
      letter = pinyin(first, {
        pattern: 'first',
        toneType: 'none',
      }).toUpperCase()
      if (!/[A-Z]/.test(letter)) letter = '#'
    }
    if (!groups[letter]) groups[letter] = []
    groups[letter].push(item)
  })
  return groups
})

// 只显示有数据的字母
const availableLetters = computed(() =>
  allLetters.filter((l) => grouped.value[l]?.length),
)

const currentLetter = ref(availableLetters.value[0] || 'A')

function scrollToLetter(letter) {
  const el = document.getElementById('group-' + letter)
  if (el) {
    el.scrollIntoView({ behavior: 'smooth', block: 'start' })
    currentLetter.value = letter
  }
}

function handleSelect(item) {
  emit('select', item)
}
function closeFun(){
  emit('update:visible', false)
}
</script>

<style scoped>
.contact-book {
  display: flex;
  width: 80%;
  height: 90%;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.6);
  overflow: hidden;
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 100;
}

.contact-list {
  flex: 1;
  overflow-y: auto;
  padding: 24px 16px 24px 24px;
}
.contact-list::-webkit-scrollbar-thumb {
  background: #cccccc;
}
.contact-list::-webkit-scrollbar-thumb:hover {
  background: #999999; /* 悬浮时可选更深一点的灰色 */
}
.letter-group {
  margin-bottom: 18px;
}

.letter-header {
  font-size: 15px;
  font-weight: bold;
  color: #00c3f4;
  margin-bottom: 6px;
  letter-spacing: 2px;
}

.contact-item:hover {
  background: #e6f7ff;
  color: #00c3f4;
}

.letter-index {
  width: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f7f7f7;
  border-left: 1px solid #eee;
  user-select: none;
}

.letter-index-item {
  padding: 2px 0;
  cursor: pointer;
  color: #888;
  font-size: 12px;
  width: 100%;
  text-align: center;
  border-radius: 4px;
  margin: 1px 0;
  transition: background 0.2s, color 0.2s;
}

.letter-index-item.active,
.letter-index-item:hover {
  background: #00c3f4;
  color: #fff;
  font-weight: bold;
}

.letter-index-item.disabled {
  color: #ccc;
  cursor: not-allowed;
  background: none;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s cubic-bezier(.55,0,.1,1);
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}
</style>