<template>
  <div class="titleLine flex flex-items-center flex-justify-between p-16px h-70px">
          <span class="font-size-18px color-#333333 font-bold">{{ menuData?.name }}</span>
          <div class="flex flex-items-center bg-#E6F7F480 rounded-50px font-size-15px p-10px h-36px">
            <div class="flex flex-items-center flex-justify-center flex-items-center color-#333333 min-w-80px w-max-content cursor-pointer">
              中文
              <el-icon class="ml-5" color="#000000" size="16px"><ArrowDown /></el-icon>
            </div>
            <img src="@renderer/assets/exchange.png" class="w-18px h-18px cursor-pointer ml-6 mr-6" alt="">
            <div class="flex flex-items-center flex-justify-center flex-items-center color-#333333 min-w-80px w-max-content cursor-pointer">
              中文
              <el-icon class="ml-5" color="#000000" size="16px"><ArrowDown /></el-icon>
            </div>
          </div>
        </div>
</template>

<script setup>
const props = defineProps({
  menuData: {
    type: Object,
    default: () => {}
  }
})
</script>

<style>

</style>