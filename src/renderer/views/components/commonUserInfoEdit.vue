<template>
  <div class="commonUserInfoEdit">
    <el-dialog v-model="isUserInfoShow" width="600" align-center class="editUserInfo" title="编辑资料">
      <div class="flex flex-col flex-justify-center flex-items-center" v-loading="isLoading">
        <div class="headPic">
          <el-image class="w-80px h-80px rounded-50%" :src="avatarUrl" fit="cover" />
          <el-upload ref="upload" class="upload-demo" accept=".png,.jpg,.jpeg" :limit="1" :on-exceed="handleExceed" :auto-upload="false" :on-change="handleChange">
            <template #trigger>
              <div class="cameraView flex flex-items-center flex-justify-center">
                <el-icon class="cameraIcon" color="#00C3F4" size="30">
                  <Camera />
                </el-icon>
              </div>
            </template>
          </el-upload>
        </div>
        <div class="flex color-#333333 mt-10">
          翻翻号：{{ userInfo.u_s_id }} 
          <img @click="copyFFCode" class="w-20px h-20px ml-10 cursor-pointer" src="@renderer/assets/copyIcon.png" alt="">
        </div>
        <div class="flex color-#333333">
          <div class="flex p-10px">
            <span class="label">同传时长：</span>
            <span class="val font-500">{{ Number(userInfo.cards_total_remaining / 60).toFixed(2) }} 分</span>
          </div>
          <div class="flex p-10px">
            <span class="label">通用点数：</span>
            <span class="val font-500">{{ userInfo.balance }} 点</span>
          </div>
        </div>
        <el-form label-width="auto" class="mt-20">
          <el-form-item label="昵称">
            <el-input v-model="userInfoEditData.n_name" style="width: 260px;" />
          </el-form-item>
          <el-form-item label="手机号" v-if="userInfo.mobile">
            <div class="bindEmailLine">
              <el-input v-model="userInfo.mobile" style="width: 260px;" disabled />
            </div>
          </el-form-item>
          <el-form-item label="邮箱" v-if="userInfo.email">
            <div class="bindEmailLine">
              <el-input v-model="userInfo.email" style="width: 260px;" disabled />
            </div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="flex flex-justify-center">
          <el-button type="primary" @click="isUserInfoShow = false">取消</el-button>
          <el-button type="primary" @click="saveUserInfo('save')">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref,onMounted } from 'vue'
import { Camera } from '@element-plus/icons-vue'
import useClipboard from 'vue-clipboard3'
import { getUserInfo,editUserInfo } from '@renderer/api/index'
import { ElMessage, genFileId } from 'element-plus'
import type { UploadInstance, UploadProps, UploadRawFile } from 'element-plus'
const emit = defineEmits()
const upload = ref<UploadInstance>()

const handleExceed: UploadProps['onExceed'] = (files) => {
  upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
}
const handleChange = (file:any) => {
  console.log(file)
  userInfoEditData.value.avatar = file.raw
  avatarUrl.value = URL.createObjectURL(file.raw)
}

const userInfo = ref<any>({})
const userInfoEditData = ref<any>({
  n_name: '',
  avatar: ''
})
const avatarUrl = ref('')
const isUserInfoShow = ref(false)
const getUserInfoFun = async () => {
  let userRes:any = await getUserInfo()
  if(userRes.code == 'YFF0'){
    userInfo.value = userRes.data.user
    let {n_name,avatar} = userInfo.value
    userInfoEditData.value = {n_name,avatar}
    avatarUrl.value = avatar
    localStorage.setItem('userInfo',JSON.stringify(userInfo.value))
    emit('updateUserInfo')
  }
}
const isLoading = ref(false)
const saveUserInfo = (type: String) => {
  let requestData = null
  if(type == 'save'){
    if(userInfoEditData.value.avatar == userInfo.value.avatar && userInfoEditData.value.n_name == userInfo.value.n_name){
      ElMessage.warning('信息未修改~')
      return
    }
    if(userInfoEditData.value.avatar == userInfo.value.avatar){
      delete userInfoEditData.value.avatar  // 头像没修改则去掉头像字段 防止保存接口报错
      requestData = JSON.parse(JSON.stringify(userInfoEditData.value))
    }else{
      requestData = JSON.parse(JSON.stringify(userInfoEditData.value))  // 不能深拷贝File文件 还有function、undfined、Date等等
      // 深拷贝一个头像文件
      const copiedFile = new File([userInfoEditData.value.avatar], userInfoEditData.value.avatar.name, {
        type: userInfoEditData.value.avatar.type,
      })
      requestData.avatar = copiedFile
    }
  }
  isLoading.value = true
  editUserInfo(requestData).then((res:any) => {
    if(res.code == 'YFF0'){
      isLoading.value = false
      ElMessage.success('修改成功')
      isUserInfoShow.value = false
      getUserInfoFun()
    }
  })
}

const { toClipboard } = useClipboard()
const copyFFCode = async () => {
  try {
    await toClipboard(userInfo.value.u_s_id)
    ElMessage.success('复制成功')
  } catch (err) {
    ElMessage.error('复制失败')
  }
}
defineExpose({
  isUserInfoShow,
  getUserInfoFun
})
onMounted(()=>{
  getUserInfoFun()
})
</script>

<style lang="scss" scoped>
.commonUserInfoEdit{
  .headPic{
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    position: relative;
    .avatarImg{
      width: 100%;
      height: 100%;
    }
    &:hover{
      .cameraView{
        background: rgba(0,0,0,0.5);
        opacity: 1;
      }
    }
    .cameraView {
      width: 100%;
      height: 100%;
      background: transparent;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      transition: all 0.4s;
      opacity: 0;
      cursor: pointer;
    }
  }
}
</style>