<template>
  <div class="floatWin w-100vw h-100vh">
    <div class="topBlock w-100vw h-60px position-fixed top-0 left-0 p-10px flex justify-between items-center">
      <div class="flex flex-items-center color-white">
        <div class="font-size-18px font-bold">{{ subMenu?.key == 'aitc' ? 'AI同传' : '高精同传' }}{{ subMenu?.key == 'aitc' ? `-${curAIModel.value}` : '' }}</div>
        <div class="langOuter flex flex-items-center bg-#0008 rounded-50px font-size-15px p-10px h-30px ml-20px font-bold" :class="{'disabled': true}">
          <div @click="selectLang('source')" class="flex flex-items-center flex-justify-center flex-items-center color-#white min-w-80px pl-16 w-max-content cursor-pointer">
            {{sourceLang?.label}}
            <!-- <el-icon class="ml-5" color="#000000" size="16px"><ArrowDown /></el-icon> -->
          </div>
          <img src="@renderer/assets/exchange.png" class="w-18px h-18px cursor-pointer ml-16 mr-16" alt="" @click="exchangeLang">
          <div @click="selectLang('target')" class="flex flex-items-center flex-justify-center flex-items-center color-#white min-w-80px pr-16 w-max-content cursor-pointer">
            {{targetLang?.label}}
            <!-- <el-icon class="ml-5" color="#000000" size="16px"><ArrowDown /></el-icon> -->
          </div>
        </div>
      </div>
      <span class="color-white position-absolute left-50% translate-x--50% font-size-18">{{ formatTimeAsHMS(seconds) }}</span>
      <div class="flex flex-items-center btnBlock">
        <img src="@renderer/assets/addIcon.png" class="toolIcon cursor-pointer" alt="" @click="changeFontSize('add')">
        <img src="@renderer/assets/reduceIcon.png" class="toolIcon cursor-pointer" alt="" @click="changeFontSize('reduce')">
        <div class="position-relative w-20px h-20px">
          <img src="@renderer/assets/shadowIcon.png" class="toolIcon cursor-pointer" alt="">
          <span class="position-absolute right--5px bottom-0 color-white">+</span>
        </div>
        <div class="position-relative w-20px h-20px">
          <img src="@renderer/assets/shadowIcon.png" class="toolIcon cursor-pointer" alt="">
          <span class="position-absolute right--5px bottom-0 color-white">-</span>
        </div>
        <img v-if="!stapleStatus" src="@renderer/assets/staple.png" class="staple toolIcon cursor-pointer" alt="" @click="changeStapleStatus(true)">
        <img v-if="stapleStatus" src="@renderer/assets/cancelStaple.png" class="staple toolIcon cursor-pointer" alt="" @click="changeStapleStatus(false)">
        <img src="@renderer/assets/returnIcon.png" class="toolIcon cursor-pointer mt--10px mr-5px" alt="" @click="closeFun">
      </div>
    </div>
    <div ref="innerTextView" class="w-100% rounded-8px box-sizing-border-box pl-10px pr-10px overflow-auto position-relative top-60px color-white font-bold" :style="{'height': `calc(100% - 60px)`, 'font-size': `${fontSize}px`}">
      <div v-for="(item,index) in resTextArr" :key="index">
        <div>{{ item.originalText }}</div>
        <div>{{ item.translatedText }}</div>
        <br>
      </div>
    </div>
  </div>
</template>

<script setup>
const { ipcRendererChannel } = window
import { ref, onMounted, onUnmounted } from 'vue'
import { CircleCloseFilled,ArrowDown } from '@element-plus/icons-vue'
import { formatTimeAsHMS } from '@renderer/utils/utils'

const closeFun = () => {
  if(intervalId){
    clearInterval(intervalId)
    intervalId = null
  }
  ipcRendererChannel.CloseFloatWin.invoke()
}
const selectLang = (type) => {
  return
}
const exchangeLang = () => {
  return
}
const stapleStatus = ref(false)
const changeStapleStatus = (status) => {
  stapleStatus.value = status
  ipcRendererChannel.SetFloatWinAlwaysOnTop.invoke(status)
}
const fontSize = ref(15)
const changeFontSize = (type) => {
  if(resTextArr.value.length == 0) return
  if(type === 'add'){
    fontSize.value++
  }else{
    fontSize.value--
  }
  console.log(fontSize.value)
}
const sourceLang = ref('')
const targetLang = ref('')
const resTextArr = ref([])
// const isRunning = ref(false)
const seconds = ref('')
const curAIModel = ref('')
const subMenu = ref(null)
let intervalId = null
const handleGetData = (event, data) => {
  console.log('收到主进程发来的数据:', JSON.parse(data));
  let dataObj = JSON.parse(data)
  if(dataObj.type === 'open'){
    sourceLang.value = dataObj.sourceLang
    targetLang.value = dataObj.targetLang
    curAIModel.value = dataObj.curAIModel
    subMenu.value = dataObj.subMenu
    seconds.value = dataObj.seconds
    if(dataObj.isRunning){
      intervalId = setInterval(() => {
        seconds.value++
      }, 1000)
    }
  }
  resTextArr.value = dataObj.resTextArr
  handleScrollToBottom()
}
const innerTextView = ref(null)
const isAtBottom = () => {
  if (innerTextView.value) {
    const { scrollTop, scrollHeight, clientHeight } = innerTextView.value
    return scrollTop + clientHeight >= scrollHeight - 1 // 允许 1px 的误差
  }
  return false
};
const handleScrollToBottom = () => {
  if (innerTextView.value && !isAtBottom()) {
    innerTextView.value.scrollTo({
      top: innerTextView.value.scrollHeight, // 滚动到底部
      behavior: 'smooth', // 平滑滚动
    })
  }
}
onMounted(() => {
  ipcRendererChannel.WorkbenchToFloat.on(handleGetData)
});
onUnmounted(()=>{
  ipcRendererChannel.WorkbenchToFloat.removeListener(handleGetData)
})
</script>

<style lang="scss">
.floatWin{
  background: rgba(0,0,0,0.8);
  box-sizing: border-box;
  border-radius: 8px;
  .topBlock{
    -webkit-app-region: drag;
  }
  .btnBlock,.langOuter{
    -webkit-app-region: no-drag;
  }
  .disabled{
    cursor: not-allowed !important;
    // opacity: 0.6;
  }
  .staple{
    transform: rotate(-45deg);
  }
  .toolIcon{
    width: 20px;
    height: 20px;
    margin-top: -10px;
    margin-right: 10px;
  }
}
</style>