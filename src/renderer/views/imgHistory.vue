<template>
  <div class="tcHistory overflow-auto position-relative">
    <div class="w-120px h-30px rounded-6px flex flex-items-center flex-justify-center color-white bg-#00c3f4 cursor-pointer font-size-15px mt-10" style="border-radius: 50px;" @click="getList('refresh')">手动刷新</div>
    <el-table v-loading="tableLoading" style="width: 100%" :data="tableData" height="calc(100% - 114px)" width="100%" class="mt-20px" header-row-class-name="tableHeader" empty-text="暂无数据">
      <el-table-column prop="original_filename" label="文档名称" width="200px" show-overflow-tooltip fixed="left" />
      <el-table-column prop="created_at" label="创建时间" width="180px" align="center" />
      <el-table-column prop="file_size_formatted" label="大小" width="100px" align="center" />
      <el-table-column prop="status" label="翻译状态" width="100px" align="center">
        <template #default="scope">
          <span>{{ formatStatus(scope.row.status) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="到期时间" width="180px" align="center">
        <template #default="scope">
          <span>{{ scope.row.expires_at }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="action" label="操作" width="100px" align="center" fixed="right">
        <template #default="scope">
          <div class="flex flex-items-center flex-justify-center color-#00c3f4" v-if="scope.row.status != 'failed' && !scope.row.is_deleted">
            <a class="m-3px" @click="handleItemFun(scope.row,'preview')">预览</a>
            <a class="m-3px" @click="handleItemFun(scope.row,'download')">下载</a>
          </div>
          <div v-else class="color-red font-weight-500">文件已失效</div>
        </template>
      </el-table-column>
    </el-table>
    <div class="position-absolute bottom-0">
      <el-pagination @change="changePage" background layout="prev, pager, next" :total="total" :hide-on-single-page="false" v-model:current-page="curPage" :page-size="pageSize" />
    </div>
    <el-image hidden ref="imgRef" :src="imgUrl" fit="contain" :preview-src-list="previewImgArr" :zoom-rate="1.2" :max-scale="7" :min-scale="0.2">
      <template #error>
        <span></span>
      </template>
    </el-image>
  </div>
</template>

<script setup>
const { ipcRendererChannel } = window
import { ref, onMounted } from 'vue'
import { getTranslateHistory,getHistoryDetail } from '@renderer/api/index'
const tableData = ref([])
const tableLoading = ref(false)
const total = ref(0)
const pageSize = ref(10)
const getList = (val) => {
  tableLoading.value = true
  if(val) curPage.value = 1
  let obj = {
    page: curPage.value,
    page_size: pageSize.value,
    file_type: 'image',
  }
  getTranslateHistory(obj).then(res => {
    tableLoading.value = false
    tableData.value = res.data.results
    total.value = res.data.count
  })
}

const curPage = ref(1)
const changePage = (e) => {
  curPage.value = e
  getList()
}
const imgUrl = ref('')
const previewImgArr = ref([])
const imgRef = ref(null)
 const handleItemFun = async(row,handleType) => {
  if(row.status == 'pending'){
    ElMessage.warning('文件正在处理中，请稍后再试')
    return
  }
  if(row.status == 'failed' || row.is_deleted){
    ElMessage.error('文件损坏或已删除，无法操作，请联系客服')
    return
  }
  let type = row.file_type
  let res = await getHistoryDetail({file_t_id: row.file_t_id})
  if(res.code != 'YFF0'){
    return
  }
  let {file_path,oss_url,original_filename,text_file_url} = res.data
  console.log(res)
  if(handleType == 'preview'){
    imgUrl.value = oss_url
    previewImgArr.value = [oss_url]
    setTimeout(() => {
      if (imgRef.value) {
        const imageElement = imgRef.value?.$el.querySelector('img');
        imageElement.click()
      }
    }, 200)
  }
  if(handleType == 'download'){
    ipcRendererChannel.DownloadOssFile.invoke({url: oss_url, defaultName: original_filename}).then(res=>{
      if(res.success){
        ElMessage.success('下载成功')
      }else{
        ElMessage.error(res.message)
      }
    })
  }
}
const formatStatus = (val) => {
  let str = "未知"
  switch(val){
    case 'success':
      str = '成功'
      break;
    case 'failed':
      str = '失败'
      break;
    case 'pending':
      str = '处理中'
      break;
  }
  return str
}
onMounted(() => {
  getList()
})

</script>

<style lang="scss">
.tcHistory{
  width: 100%;
  height: 100%;
  .checked{
    background-color: #00c3f4 !important;
    color: #fff !important;
    box-shadow: 0 0 10px 2px rgba(0, 195, 244, 0.5) !important;
  }
  .tableHeader{
    background-color: #f5f5f5 !important;
    color: #333 !important;
  }
  .el-table__row{
    height: 46px;
  }
}
</style>