<template>
  <div class="imgTranslation w-100% h-100% rounded-8px">
    <el-upload v-if="!isUploadSuccessShow" class="uploadBlock" ref="upload" accept=".jpg,.png,.gif" drag :limit="1" :show-file-list="false" :auto-upload="false" :on-exceed="handleExceed" :on-change="handleChange">
      <el-icon class="el-icon--upload" color="#c1eaf2"><upload-filled /></el-icon>
      <div class="font-size-15px color-#999999">点击或拖拽上传</div>
      <div class="mt-30">
        <img class="w-40px h-40px" src="@renderer/assets/jpgIcon.png" alt="">
        <img class="w-40px h-40px" src="@renderer/assets/pngIcon.png" alt="">
        <img class="w-40px h-40px" src="@renderer/assets/imgIcon.png" alt="">
      </div>
      <div class="font-size-14px color-#333333 mt-10">
        支持格式：jpg / png / gif 格式
      </div>
    </el-upload>
    <div v-else class="uploadSuccess w-100% h-300px flex flex-items-center flex-justify-center border-1px border-#85D3E2 border-solid rounded-8px overflow-hidden" v-loading="isTranslationLoading" element-loading-text="正在翻译中，稍后可在历史记录中查看~">
      <div v-if="isTranslateSuccess" class="position-relative flex flex-col flex-items-center flex-justify-center rounded-8px">
        <el-image ref="resImgRef" class="resImg w-160px h-160px" :src="translateResImgData.url" fit="contain" :preview-src-list="translateResImgData.previewUrlArr" :zoom-rate="1.2" :max-scale="7" :min-scale="0.2" />
        <div class="flex flex-items-center flex-justify-center mt-20">
          <el-button class="cursor-pointer mr-10px ml-10px" type="primary" round size="" @click="againUpload">再次上传</el-button>
          <el-button class="cursor-pointer mr-10px ml-10px" type="primary" round size="" @click="downloadImg">下载图片</el-button>
        </div>
      </div>
      <div v-else class="position-relative flex flex-col flex-items-center flex-justify-center rounded-8px">
        <el-image style="width: 100px; height: 100px" :src="curUploadFileUrl" fit="cover"></el-image>
        <el-icon class="position-absolute top--10px right--10px cursor-pointer" color="#FC6050" size="20px" @click="handleDeleteFile"><CircleCloseFilled /></el-icon>
        <el-button class="cursor-pointer mt-20" type="primary" round size="" @click="startTranslate">开始翻译</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { ipcRendererChannel } = window
import { ref } from 'vue'
import { UploadFilled, CircleCloseFilled } from '@element-plus/icons-vue'
import { ElMessage,genFileId,ElMessageBox } from "element-plus"
import type { UploadInstance, UploadProps, UploadRawFile } from 'element-plus'
import { translateImage, getHistoryDetail } from '@renderer/api/index'
const props = defineProps({
  targetLang: {
    type: Object,
    default: () => ({})
  },
  sourceLang: {
    type: Object,
    default: () => ({})
  }
})
const upload = ref<UploadInstance>()
const handleExceed: UploadProps['onExceed'] = (files) => {
  upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
}
const curUploadFile = ref<any>(null)
const isUploadSuccessShow = ref(false)
const curUploadFileUrl = ref('')
const handleChange = async (file: any) => {
  console.log('文件变化:', file)
  let size = file.size / 1024 / 1024  // MB
  if(size > 4){
    ElMessage.error('文件大小不能超过4MB')
    return
  }
  curUploadFile.value = file
  curUploadFileUrl.value = URL.createObjectURL(file.raw)
  isUploadSuccessShow.value = true
}
const handleDeleteFile = () => {
  curUploadFile.value = null
  curUploadFileUrl.value = ''
  isUploadSuccessShow.value = false
}
const isTranslationLoading = ref(false)
const startTranslate = () => {
  let obj = {
    image: curUploadFile.value.raw,
    target_language: props.targetLang.languageCode || props.targetLang.languageTarget,
    source_language: props.sourceLang.languageCode || props.sourceLang.languageSource,
  }
  isTranslationLoading.value = true
  translateImage(obj).then((res: any)=>{
    if(res.code == 'YFF0'){
      isTranslationLoading.value = true
      getTranslateResFun(res.data.file_t_id,'image')
    }else{
      isTranslationLoading.value = false
    }
  })
}
const resImgRef = ref<any>(null)
const loopTimer = ref<any>(null)
const translateResImgData = ref<any>({})
const isTranslateSuccess = ref(false)
const getTranslateResFun = async (file_t_id:any, type: String) => {
  getHistoryDetail({file_t_id}).then(async (res:any)=>{
    if(res.code == 'YFF007' || res.data.status == 'pending'){
      loopTimer.value = setTimeout(()=>{
        getTranslateResFun(file_t_id,type)
      },10000)
    }else if(res.data.status == 'failed'){
      ElMessage.error('翻译失败，请稍后重试~')
      isTranslationLoading.value = false
    }else if(['YFF008','YFF0'].indexOf(res.code) != -1){
      // isUploadSuccessShow.value = false
      isTranslationLoading.value = false
      // const { data: { downloadBaseUrl } } = await ipcRendererChannel.GetEnvInfo.invoke()
      // let downLoadUrl = `${downloadBaseUrl}${res.data.file_path.startsWith('/') ? res.data.file_path : '/' + res.data.file_path}`

      if(type == 'image'){
        translateResImgData.value.downLoadUrl = res.data.oss_url
        translateResImgData.value.name = res.data.original_filename
        translateResImgData.value.url = res.data.oss_url
        translateResImgData.value.previewUrlArr = [res.data.oss_url]
        isTranslateSuccess.value = true
        setTimeout(() => {
          if (resImgRef.value) {
            const imageElement = resImgRef.value?.$el.querySelector('img');
            imageElement.click()
          }
        }, 200)
      }
    }else{
      ElMessage.error('翻译失败，请稍后重试~')
      isTranslationLoading.value = false
    }
  })
}
const againUpload = () => {
  isTranslateSuccess.value = false
  translateResImgData.value = {}
  isUploadSuccessShow.value = false
  isTranslationLoading.value = false
  curUploadFile.value = null
  curUploadFileUrl.value = ''
}
const downloadImg = () => {
  ipcRendererChannel.DownloadOssFile.invoke({url: translateResImgData.value.downLoadUrl, defaultName: translateResImgData.value.name}).then(res=>{
    if(res.success){
      ElMessage.success('下载成功')
    }else{
      ElMessage.error(res.message)
    }
  })
}
</script>

<style lang="scss" scoped>
.imgTranslation{
  .uploadBlock{
      :deep(.el-upload-dragger){
        width: 100%;
        height: 300px;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        // border: 1px dashed #85D3E2 !important;
      }
    }
}
</style>