<template>
  <div class="app-container">
    <div class="audio-list">
      <div class="audio-item" v-for="(item, index) in messages" :key="index" @click="downFile(index)">
        file{{ index }} - {{ item.length }}
      </div>
    </div>
    <div class="btns">
      <el-Button type="primary" @click="start">开始录音</el-Button>
      <el-Button type="danger" @click="stop">停止录音</el-Button>
      <span> {{ recording ? "录制中..." : "已暂停" }} </span>
    </div>
  </div>
</template>

<script setup>
//const { ipcRenderer } = require("electron")
//import { ElButton } from "element-plus"
import { ref, onMounted, onUnmounted } from "vue"

const messages = ref([])
const recording = ref(false)
onMounted(() => {

  window.audioAPI.onStartRecording((event, uintPatam) => {
    if (!uintPatam.success) {
      stop()
      console.log("录音启动失败：", uintPatam.err)
    } else {
      console.log("录音启动成功：", uintPatam)
      messages.value.push(uintPatam.data)
      recording.value = true
    }
  })

  window.audioAPI.onStopRecording((event, uintPatam) => {
    console.log("成功停止", event, uintPatam)
    recording.value = false
  })
})
onUnmounted(() => {
  window.audioAPI.removeAllListeners('start-audio-recording')
  window.audioAPI.removeAllListeners('stop-audio-recording')
})
const start = () => {
  window.audioAPI.startRecording()
}
const stop = () => {
  window.audioAPI.stopRecording()
}
const downFile = (index) => {
  console.log("uint8Array", messages.value[index])
}
</script>

<style scoped>
.app-container {
  height: 100%;
}

.app-container .audio-list {
  overflow: auto;
  height: 80%;

}

.app-container .btns {
  height: 20%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.audio-item {
  cursor: pointer;
}
</style>
