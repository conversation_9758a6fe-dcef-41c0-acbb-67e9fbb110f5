<template>
  <div class="login box-border">
    <div class="topBlock h-30px w-100vw position-fixed top-0 left-0"></div>
    <div class="main flex box-border">
      <div class="left w-50vw h-100vh flex-base box-border overflow-hidden">
        <el-carousel height="100vh">
          <el-carousel-item v-for="item in [1,2]" :key="item">
            <img :src="item == 1 ? banner1 : banner2" class="w-100% h-100%" alt="">
          </el-carousel-item>
        </el-carousel>
        <!-- <div class="w-100% h-100% ">上海译翻充天科技有限公司，一家在翻译领域独树一帜的高科技企业，汇聚了一群充满活力、专业精湛的研发精英。在全球化浪潮的推动下，跨国界的人员交流与商业合作愈发密切，语言差异成为了不可忽视的挑战。</div> -->
      </div>
      <div class="right flex flex-items-center w-50vw h-100vh p-40px flex-col box-border" style="box-sizing: border-box;">
        <img src="@renderer/assets/discd_logo.jpg" class="w-50px h-50px rounded-6px logo mt-10" alt="">
        <div class="font-size-16px mt-10 font-bold color-#333333 h-30px">译翻翻</div>
        <div class="w-100% flex flex-col flex-items-center flex-justify-center mt-20" style="height: calc(100% - 200px)">
          <div v-if="loginType === 'pc'" class="w-100%">
            <div class="font-size-14px text-align-left w-100% font-500 color-#333333">手机号 / 邮箱</div>
            <el-input class="input w-100% h-30 mt-4" v-model="userName" placeholder="请输入手机号或邮箱"></el-input>
            <div class="font-size-14px mt-12 text-align-left w-100% font-500 color-#333333">验证码</div>
            <div class="flex flex-items-center flex-justify-between w-100% h-30 mt-4">
              <el-input style="width: 60%;" class="input h-30" v-model="code" placeholder="请输入验证码"></el-input>
              <el-button :style="{opacity: getCodeData.canSend ? 1 : 0.6}" class="button" style="height: 100%;font-size: 13px;" type="primary" @click="getCodeFun">{{ getCodeData.btnText }}</el-button>
            </div>
            <label class="checkbox flex flex-items-center w-100% mt-10 font-size-12px cursor-pointer">
              <el-checkbox v-model="agreementChecked" label="" />
              我已阅读并同意 <span class="color-#00C3F4" @click.stop.prevent="getUserAgreementFun">《用户协议》</span>和<span class="color-#00C3F4" @click.stop.prevent="getPrivacyAgreementFun">《隐私政策》</span>
            </label>
            <el-button :style="{opacity: canClickLogin ? 1 : 0.6}" class="button w-100% mt-20 font-size-16px" type="primary" size="large" round block @click="loginFun">登录</el-button>
          </div>
          <div v-if="loginType === 'scan'" class="w-100% flex flex-col flex-items-center flex-justify-center">
            <div class="font-size-16px color-#333333">请使用 <span class="font-500">译翻翻APP</span> 扫码登录</div>
            <div class="w-200px h-200px flex flex-items-center flex-justify-center" v-loading="qrcodeLoading">
              <img v-if="!qrcodeLoading && qrcodeImg" class="w-200px h-200px" :src="qrcodeImg" alt="">
              <div v-if="qrcodeInvalid" @click="getQrcodeAgain" class="cursor-pointer flex flex-items-center flex-justify-center bg-#F5F5F5 w-160px h-160px font-size-14px text-align-center">二维码已失效<br>点击刷新</div>
            </div>
          </div>
        </div>
        <div class="position-absolute bottom-20px">
          <img v-for="item in loginTypeList" :src="item.icon" :key="item.key" @click="changeLoginType(item.key)" class="w-26px h-26px ml-10 mr-10 cursor-pointer" alt="">
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive,watch } from 'vue'
const { ipcRendererChannel, crash } = window
import pc from '@renderer/assets/pc.png'
import scan from '@renderer/assets/scan.png'
// import wechat from '@renderer/assets/wechat.png'
// import iphone from '@renderer/assets/iphone.png'
import banner1 from 'src/renderer/assets/banner1.png'
import banner2 from 'src/renderer/assets/banner2.png'
import { getLoginQrcode,loopGetLoginStatus,login,getTelCode,getEmailCode,getPrivacyAgreement,getUserAgreement } from '../api/index'

const loginTypeList = ref([
  {
    icon: pc,
    key: 'pc',
  },
  {
    icon: scan,
    key: 'scan',
  },
  // {
  //   icon: wechat,
  //   key: 'wechat',
  // },
  // {
  //   icon: iphone,
  //   key: 'iphone',
  // },
])
const loginType = ref('pc')
const qrcodeImg = ref('')
const qrcodeToken = ref('')
const qrcodeLoading = ref(false)
const qrcodeInvalid = ref(false)
const changeLoginType = (key) => {
  loginType.value = key
  if(key === 'scan'){
    getQrcodeFun()
  }else{
    clearInterval(loopTimer.value)
  }
}
const getQrcodeFun = () => {
  qrcodeLoading.value = true
  getLoginQrcode().then((res) => {
    let { qr_code,login_token } = res.data
    qrcodeImg.value = `data:image/png;base64,${qr_code}`
    qrcodeToken.value = login_token
    loopTimer.value = setInterval(() => {
      loopGetLoginStatusFun()
    },3000)
    qrcodeLoading.value = false  // 二维码框中loading隐藏
    qrcodeInvalid.value = false
  })
}
const loopTimer = ref(null)
const loopGetLoginStatusFun = () => {
  loopGetLoginStatus({login_token: qrcodeToken.value}).then(res => {
    let { status,user } = res.data
    if(status === 'success'){
      loginNext(user)
    }else if(status == 'pending'){
      // qrcodeImg.value = null
      // qrcodeInvalid.value = true
      // clearInterval(loopTimer.value)
    }else{
      qrcodeImg.value = null
      qrcodeInvalid.value = true
      clearInterval(loopTimer.value)
    }
  })
}
const getQrcodeAgain = () => {
  qrcodeLoading.value = true  // 二维码框中loading显示
  qrcodeInvalid.value = false
  getQrcodeFun()
}
const getCodeData = reactive({
  btnText: '获取验证码',
  canSend: false,
  time: 60
})
const getCodeFun = () => {
  // if(!getCodeData.canSend) return
  if(userName.value == ''){
    ElMessage.error('请输入手机号或邮箱')
    return
  }
  let emailExg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
  let phoneExg = /^1[3-9]\d{9}$/
  let type = ''
  if(emailExg.test(userName.value)){
    type = 'email'
  }else if(phoneExg.test(userName.value)){
    type = 'tel'
  }else{
    ElMessage.error('请输入正确的手机号或邮箱')
    return
  }
  if(getCodeData.canSend){
    if(type == 'email'){
      let obj = {
        email: userName.value,
        type: "login"
      }
      getEmailCode(obj).then(res=>{
        if(res.code == 'YFF0'){
          handleCountDownTime()
        }
      })
    }else{
      let obj = {
        mobile: userName.value,
        type: "login"
      }
      getTelCode(obj).then(res=>{
        if(res.code == 'YFF0'){
          handleCountDownTime()
        }
      })
    }
  }
}
const handleCountDownTime = () => {
  getCodeData.canSend = false
  getCodeData.btnText = `${getCodeData.time} s后获取`
	let timer = setInterval(() => {
    if (getCodeData.time > 1 && getCodeData.time <= 60) {
	    getCodeData.time = getCodeData.time - 1
	    getCodeData.canSend = false
	    getCodeData.btnText = `${getCodeData.time} s后获取`
    } else {
	    getCodeData.time = 60
	    getCodeData.canSend = true
	    getCodeData.btnText = `获取验证码`
	    clearInterval(timer)
    }
  }, 1000)
}
const checkData = () => {
  if(userName.value == ''){
    ElMessage.error('请输入手机号或邮箱')
    return false
  }
  if(loginType.value == 'pc'){
    let emailExg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
    let phoneExg = /^1[3-9]\d{9}$/
    if(!emailExg.test(userName.value) && !phoneExg.test(userName.value)){
      ElMessage.error('请输入正确的手机号或邮箱')
      return false
    }
    if(code.value == ''){
      ElMessage.error('请输入验证码')
      return false
    }
    if(!agreementChecked.value){
      ElMessage.error('请勾选服务协议')
      return false
    }
    return true
  }
}
const userName = ref('')
const code = ref('')
const agreementChecked = ref(false)
const loginFun = () => {
  if(!checkData()){
    const data = {
      url: '/main',
    }
    console.log(ipcRendererChannel)
    ipcRendererChannel.OpenWin.invoke(data)
    ipcRendererChannel.CloseMainWindow.invoke();
    return
  }
  if(loginType.value == 'pc'){
    let type = ''
    let emailExg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
    let phoneExg = /^1[3-9]\d{9}$/
    if(emailExg.test(userName.value)){
      type = 'email'
    }else if(phoneExg.test(userName.value)){
      type = 'mobile'
    }
    let obj = {}
    if(type == 'email'){
      obj = {
        email: userName.value,
        email_code: code.value,
        login_type: 'email'
      }
    }else{
      obj = {
        mobile: userName.value,
        mobile_code: code.value,
        login_type: 'mobile'
      }
    }
    login(obj).then(res=>{
      let { user } = res.data
      if(res.code == 'YFF0'){
        loginNext(user)
      }
    })
  }
}
const loginNext = (user) => {
  localStorage.setItem('userInfo',JSON.stringify(user))
  localStorage.setItem('token',user.token)
  clearInterval(loopTimer.value)
  ipcRendererChannel.CreateUserFolder.invoke(user.user_id).then(res => {
    ipcRendererChannel.CloseMainWindow.invoke()
    const data = {
      url: '/main',
    }
    ipcRendererChannel.OpenWin.invoke(data)
  })
}
const getUserAgreementFun = async () => {
  let res = await getUserAgreement()
  const data = {
    url: res.data.agreement_url,
  }
  ipcRendererChannel.OpenWin.invoke(data)
}
const getPrivacyAgreementFun = async () => {
  let res = await getPrivacyAgreement()
  const data = {
    url: res.data.agreement_url,
  }
  ipcRendererChannel.OpenWin.invoke(data)
}
const canClickLogin = ref(false)
watch(userName,(newVal)=>{
  if(newVal == ''){
    getCodeData.canSend = false
  }else{
    getCodeData.canSend = true
  }
})
watch([userName,code],([newVal1,newVal2])=>{
  if(newVal1 == '' || newVal2 == ''){
    canClickLogin.value = false
  }else{
    canClickLogin.value = true
  }
})
</script>

<style lang="scss" scoped>
.login{
  
  .logo{
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.2);
  }
  :deep(.el-input__wrapper){
    background-color: #F5F5F5;
    font-size: 14px;
    color: #000000;
  }
  :deep(.el-input__inner){
    color: #000000 !important;
    border: none;
    // font-weight: bold;
  }
  // .button,.input,label{
  //   -webkit-app-region: no-drag;
  // }
  .topBlock{
    -webkit-app-region: drag;
  }
}
</style>
