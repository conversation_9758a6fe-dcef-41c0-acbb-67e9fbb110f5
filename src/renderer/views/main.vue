<template>
  <div class="main w-100vw h-100vh">
    <div class="topBlock h-30px w-100vw position-fixed top-0 left-0"></div>
    <div class="content flex w-100vw h-100vh">
      <div class="menu flex flex-col flex-items-center position-relative">
        <img src="@renderer/assets/discd_logo.jpg" class="w-50px h-50px rounded-6px mt-50 logo" alt="">
        <div class="menuOuter mt-20 w-100% font-size-15px color-#333333 cursor-pointer">
          <div class="menuItem flex flex-items-center flex-justify-start pl-20 h-50px line-height-50px mt-10 w-100%" :class="{'menuChecked': item.key === curMenu?.key}" v-for="item in menuList" :key="item.key" @click="handleMenuClick(item,'menu')">
            <img :src="item.icon" class="w-26px h-24px rounded-6px mr-6 menuIcon" alt="">
            <span class="menuName">{{ item.name }}</span>
          </div>
        </div>
        <div class="position-absolute bottom-0 w-100% left-0 cursor-pointer flex flex-items-center flex-justify-center flex-col">
          <img :src="userInfo.avatar" class="w-50px h-50px rounded-full" alt="" @click="showUserInfo">
          <span class="font-size-12px color-#333333 font-bold mt-6" @click="showUserInfo">{{ userInfo.n_name }}</span>
          <!-- <span class="font-size-14px color-#333333 font-bold mt-10">余额：{{userInfo.balance}} 点</span> -->
          <!-- <el-button type="primary" size="small" class="mt-10">去充值</el-button> -->
          <span class="font-size-14px color-#333333 bg-#EEEEEE mt-20px h-40px line-height-40px w-100% text-align-center font-bold" @click="logout">退出登录</span>
        </div>
      </div>
      <div class="subMenu bg-white m-10px ml-0 rounded-8px">
        <div class="menuOuter mt-10 w-100% font-size-15px color-#333333 cursor-pointer">
          <div class="menuItem flex flex-items-center flex-justify-start pl-20 h-50px line-height-50px mt-10 w-100%" :class="{'menuChecked': item.key === curSubMenu?.key}" v-for="item in curChildrenMenu" :key="item.key" @click="handleMenuClick(item,'subMenu')">
            <img :src="item.icon" class="w-26px h-26px rounded-6px mr-6 menuIcon" alt="">
            <span class="menuName">{{ item.name }}</span>
          </div>
        </div>
      </div>
      <!-- 一级菜单 180 + 二级菜单 190 + mainConten的 margin 10  -->
      <div class="mainContent flex-1 bg-white m-10px ml-0 rounded-8px" style="width: calc(100% - 380px);">
        <div class="titleLine flex flex-items-center flex-justify-between pl-16px pr-16px h-60px">
          <div>
            <span class="font-size-18px color-#333333 font-bold">{{ curSubMenu?.name }}</span>
            <span v-if="curSubMenu?.key == 'aitc' && curAIModel.value" class="font-size-18px color-#333333 font-bold"> — {{ curAIModel.label }}</span>
          </div>
          <div v-if="!['rechargeCenter','useHistory','tariffDetails'].includes(curMenu?.key) && curSubMenu?.key != 'history'" class="flex flex-items-center bg-#E6F7F480 rounded-50px font-size-15px p-10px h-36px" :class="{'disabled': isRunning}">
            <div @click="selectLang('source')" class="flex flex-items-center flex-justify-center flex-items-center color-#333333 min-w-80px pl-16 w-max-content cursor-pointer">
              {{sourceLang?.label}}
              <el-icon class="ml-5" color="#000000" size="16px"><ArrowDown /></el-icon>
            </div>
            <img src="@renderer/assets/exchange.png" class="w-18px h-18px cursor-pointer ml-16 mr-16" alt="" @click="exchangeLang">
            <div @click="selectLang('target')" class="flex flex-items-center flex-justify-center flex-items-center color-#333333 min-w-80px pr-16 w-max-content cursor-pointer">
              {{targetLang?.label}}
              <el-icon class="ml-5" color="#000000" size="16px"><ArrowDown /></el-icon>
            </div>
          </div>
        </div>
        <div class="w-100% pl-16px pr-16px" style="height: calc(100% - 66px);">
          <component ref="compRef" :is="curSubMenu?.component" :key="curSubMenu.key" :sourceLang="sourceLang" :targetLang="targetLang" :subMenu="curSubMenu" @setAIModel="curAIModel = $event" @goPay="goPay" />
        </div>
      </div>
    </div>
    <commonSelectLang :dataSource="sourceLangOpts" v-model:visible="selectLangShow" @select="handleSelectLang"></commonSelectLang>
    <commonUserInfoEdit ref="userInfoEditPop" @updateUserInfo="updateUserInfo"></commonUserInfoEdit>
  </div>
</template>

<script setup lang="ts">
import { ref,onMounted,reactive,provide,defineAsyncComponent } from 'vue'
import { ArrowDown } from '@element-plus/icons-vue'
const { ipcRendererChannel, crash } = window
import { getTextTranslateSupportLang,getTcLang,getImgTranslateSupportLang } from '@renderer/api/index'
import uploadIcon from '@renderer/assets/upload.png'
import pointsIcon from '@renderer/assets/pointsRecharge.png'
import packageIcon from '@renderer/assets/packageRecharge.png'
import textIcon from '@renderer/assets/textIcon.png'
import AI from '@renderer/assets/AI.png'
import useHistoryIcon from '@renderer/assets/history.png'
import zifeiIcon from '@renderer/assets/zifeiIcon.png'
import { ElMessageBox } from 'element-plus'

const tscy = defineAsyncComponent(() => import('@renderer/views/tscy.vue'))
const imgTranslation = defineAsyncComponent(() => import('@renderer/views/imgTranslation.vue'))
// const packageRecharge = defineAsyncComponent(() => import('@renderer/views/packageRecharge.vue'))
// const pointsRecharge = defineAsyncComponent(() => import('@renderer/views/pointsRecharge.vue'))
const tcHistory = defineAsyncComponent(() => import('@renderer/views/tcHistory.vue'))
const imgHistory = defineAsyncComponent(() => import('@renderer/views/imgHistory.vue'))
const rechargeHistory = defineAsyncComponent(() => import('@renderer/views/rechargeHistory.vue'))
const textTransition = defineAsyncComponent(() => import('@renderer/views/textTranslation.vue'))
const commonSelectLang = defineAsyncComponent(() => import('@renderer/views/components/commonSelectLang.vue'))
const tariffDetails = defineAsyncComponent(() => import('@renderer/views/tariffDetails.vue'))
const commonUserInfoEdit = defineAsyncComponent(() => import('@renderer/views/components/commonUserInfoEdit.vue'))
const rechargeCenter = defineAsyncComponent(() => import('@renderer/views/rechargeCenter.vue'))
// import commonSelectLang from '@renderer/views/components/commonSelectLang.vue'
// import textTransition from '@renderer/views/textTranslation.vue'
// import tscy from '@renderer/views/tscy.vue'
// import imgTranslation from '@renderer/views/imgTranslation.vue'
// import packageRecharge from '@renderer/views/packageRecharge.vue'
// import pointsRecharge from '@renderer/views/pointsRecharge.vue'
// import tcHistory from '@renderer/views/tcHistory.vue'
// import imgHistory from '@renderer/views/imgHistory.vue'
// import rechargeHistory from '@renderer/views/rechargeHistory.vue'

const curAIModel = ref({
  label: '',
  value: '',
})
const isRunning = ref<boolean>(false)
provide('updateRunningStatus', (newVal: boolean) => {
  isRunning.value = newVal
});

const menuList = ref([
  {
    name: '同声传译',
    key: 'tscy',
    icon: 'https://www.yffsh.com/media/banner/scene1.png',
    children: [
      {
        name: 'AI 同传',
        key: 'aitc',
        icon: AI,
        component: tscy,
      },
      {
        name: '高精同传',
        key: 'gjtc',
        icon: 'https://www.yffsh.com/media/banner/gaojing.png',
        component: tscy,
      },
      // {
      //   name: '普通同传',
      //   key: 'pttc',
      //   icon: 'https://www.yffsh.com/media/banner/putong.png',
      //   component: tscy,
      // },
      {
        name: '历史记录',
        key: 'history',
        icon: 'https://www.yffsh.com/media/banner/history.png',
        component: tcHistory,
      },
    ],
  },
  {
    name: '图片翻译',
    key: 'imgTranslation',
    icon: 'https://www.yffsh.com/media/banner/scene4.png',
    children: [
      {
        name: '上传图片',
        key: 'imgTranslation',
        icon: uploadIcon,
        component: imgTranslation,
      },
      {
        name: '历史记录',
        key: 'history',
        icon: 'https://www.yffsh.com/media/banner/history.png',
        component: imgHistory,
      },
    ],
  },
  {
    name: '文本翻译',
    key: 'textTranslation',
    icon: 'https://www.yffsh.com/media/banner/scene6.png',
    children: [
      {
        name: '文本翻译',
        key: 'textTranslation',
        icon: textIcon,
        component: textTransition,
      }
    ]
  },
  {
    name: '充值中心',
    key: 'rechargeCenter',
    icon: 'https://www.yffsh.com/media/banner/recharge.png',
    children: [
      {
        name: '套餐充值',
        key: 'package',
        icon: packageIcon,
        component: rechargeCenter,
      },
      {
        name: '点数充值',
        key: 'points',
        icon: pointsIcon,
        component: rechargeCenter,
      },
      {
        name: '充值记录',
        key: 'rechargeHistory',
        icon: 'https://www.yffsh.com/media/banner/history.png',
        component: rechargeHistory,
      },
    ],
  },
  {
    name: '消费记录',
    key: 'useHistory',
    icon: useHistoryIcon,
    children: [
      {
        name: '消费记录',
        key: 'useHistory',
        icon: useHistoryIcon,
        component: rechargeHistory,
      },
    ],
  },
  {
    name: '资费说明',
    key: 'tariffDetails',
    icon: zifeiIcon,
    children: [
      {
        name: '资费说明',
        key: 'tariffDetails',
        icon: zifeiIcon,
        component: tariffDetails,
      },
    ],
  },
])
let curMenu = ref<any>(null)
let curSubMenu = ref<any>({
  key: '',
  name: '',
  icon: '',
  component: ''
})
const compRef = ref<any>()
const curChildrenMenu = ref<Array<any>>([])
const handleMenuClick = (item: any, type: string) => {
  if(isRunning.value){
    ElMessageBox.confirm('正在同声传译中，请手动结束后再进行其他操作', '温馨提示', {
      type: 'warning',
      confirmButtonText: '强制结束',
      cancelButtonText: '取消',
    }).then(() => {
      console.log(compRef.value,'******')
      compRef.value.stopFun()
    }).catch(() => {
      console.log('取消')
    })
    return
  }
  if(type == 'menu'){
    curChildrenMenu.value = item.children
    curSubMenu.value = item.children[0]
    curMenu.value = item
  }else{
    curSubMenu.value = item
  }
  console.log(curMenu.value,curSubMenu.value)
  getLangList()
  ipcRendererChannel.CloseFloatWin.invoke()
}
const sourceLangOpts = ref<Array<any>>([])
const targetLangOpts = ref<Array<any>>([])
const sourceLang = ref<any>({})
const targetLang = ref<any>({})
let abortController: any = null
const getLangList = async () => {
  if (abortController) {
    abortController.abort()
  }
  abortController = new AbortController()
  try {
    if (curMenu.value.key == 'textTranslation') {
      let sourceRes = await getTextTranslateSupportLang({ isOriginal: 1, signal: abortController.signal })
      let targetRes = await getTextTranslateSupportLang({ isOriginal: 2, signal: abortController.signal })
      sourceLangOpts.value = sourceRes.data.map((item:any) => ({
        ...item,
        label: item.languageName,
        value: item.languageSource
      }))
      targetLangOpts.value = targetRes.data.map((item:any) => ({
        ...item,
        label: item.languageName,
        value: item.languageSource
      }))
    } else {
      let commonApi = curMenu.value.key == 'tscy' ? getTcLang : getImgTranslateSupportLang
      let res = await commonApi({ signal: abortController.signal })
      sourceLangOpts.value = targetLangOpts.value = res.data.map((item:any) => ({
        ...item,
        label: item.languageName,
        value: item.languageSource
      }))
    }
    let zhObj = sourceLangOpts.value.find((item) => {
      return item.languageCode == 'zh' || item.languageSource == 'zh' || item.languageSource == 'zh-CN'
    })
    let enObj = targetLangOpts.value.find((item) => {
      return item.languageCode == 'en' || item.languageSource == 'en' || item.languageSource == 'en-US'
    })
    sourceLang.value = zhObj
    targetLang.value = enObj
  } catch (e) {
    if (e.name === 'AbortError') {
      // 请求被取消，忽略
      console.log('请求被取消')
      return
    }
    // 其他错误
    console.error(e)
  }
}
const selectLangType = ref<string>('source')
const selectLangShow = ref<boolean>(false)
const selectLang = (type: string) => {
  if(isRunning.value){
    ElMessage.warning('正在同传中，不能修改语言')
    return
  }
  selectLangShow.value = true
  selectLangType.value = type
}
const handleSelectLang = (item: any) => {
  if(selectLangType.value == 'source'){
    sourceLang.value = item
  }else{
    targetLang.value = item
  }
  selectLangShow.value = false
}
const exchangeLang = () => {
  if(isRunning.value){
    ElMessage.warning('正在同传中，不能修改语言')
    return
  }
  let originSource = JSON.parse(JSON.stringify(sourceLang.value))
  let originTarget = JSON.parse(JSON.stringify(targetLang.value))
  sourceLang.value = originTarget
  targetLang.value = originSource
}
const userInfoEditPop = ref<any>(null)
const showUserInfo = () => {
  console.log(userInfoEditPop.value)
  userInfoEditPop.value.isUserInfoShow = true
  userInfoEditPop.value.getUserInfoFun()
}
const updateUserInfo = () => {
  userInfo.value = JSON.parse(localStorage.getItem('userInfo') || '{}')
}
const logout = () => {
  if(isRunning.value){
    ElMessageBox.confirm('正在同声传译中，请手动结束后再进行其他操作', '温馨提示', {
      type: 'warning',
      confirmButtonText: '强制结束',
      cancelButtonText: '取消',
    }).then(() => {
      console.log(compRef.value,'******')
      compRef.value.stopFun()
    }).catch(() => {
      console.log('取消')
    })
    return
  }
  ElMessageBox.confirm('确定退出登录吗？', '温馨提示', {
    type: 'warning',
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  }).then(() => {
    ipcRendererChannel.AppClose.invoke()
    // localStorage.removeItem('userInfo')
  }).catch(() => {
    console.log('取消')
  })
}
const goPay = () => {
  curMenu.value = menuList.value.find(item=>{
    return item.key == 'rechargeCenter'
  })
  curSubMenu.value = curMenu.value.children[0]
  curChildrenMenu.value = curMenu.value.children
}
const userInfo = ref<any>({})
const init = () => {
  curMenu.value = menuList.value[0]
  curSubMenu.value = menuList.value[0].children[0]
  curChildrenMenu.value = menuList.value[0].children
  getLangList()
  userInfo.value = JSON.parse(localStorage.getItem('userInfo') || '{}')
}
onMounted(()=>{
  init()
})
</script>

<style rel="stylesheet/scss" lang="scss">
.main{
  background: linear-gradient(180deg, rgba(173, 248, 255, 0.8), rgba(255, 255, 255, 0.00) 111.86%);
  .topBlock{
    -webkit-app-region: drag;
  }
  .logo{
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.2);
  }
  .menu,.subMenu{
    width: 180px;
    min-width: 180px;
    cursor: pointer;
    // border-right: 1px solid #F5F5F5;
    padding: 10px;
  }
  .menu,
  .subMenu,
  .mainContent{
    // border: 1px solid #000;
  }
  .menu .menuChecked{
    background: white;
    border-radius: 8px;
    .menuName{
      // color: #00C3F4;
      font-weight: bold;
    }
  }
  .subMenu .menuChecked{
    background: #E3F3F4;
    border-radius: 8px;
    .menuName{
      color: #12B4DC;
      font-weight: bold;
    }
  }
  .disabled{
    cursor: not-allowed !important;
    opacity: 0.6;
  }
  
}
.el-message-box__message{
  font-size: 16px;
  color: #333333;
  font-weight: 500;
}
</style>
