<template>
  <div class="packageRecharge w-100% h-100% position-relative">
    <div class="overflow-auto goodsOuter" v-loading="loading">
      <template v-for="item in goodsList" :key="item.title">
        <div class="title font-size-15px color-#333333 pb-20 font-bold">{{ item.title }}</div>
        <div class="list flex mb-20 flex-wrap">
          <div class="item flex flex-col flex-justify-center flex-items-center p-10px w-160px h-90px mr-20 mb-20 bg-#F5F5F5 rounded-8px cursor-pointer position-relative" v-for="inner in item.list" :key="inner.id" :class="{'selected': curSelectedGood?.id === inner.id}" @click="curSelectedGood = inner">
            <div class="font-size-16px font-500">{{ inner.duration_hours }} 小时</div>
            <div class="flex flex-items-center font-size-14px mt-2">￥{{ inner.price }}</div>
            <div v-if="inner.show_discount_icon" class="tag yellowTag">{{ inner.show_discount_icon ? '限时折扣' : '' }}</div>
            <div v-if="inner.show_new_discount_icon" class="tag redTag">{{ inner.show_new_discount_icon ? '新人专享' : '' }}</div>
          </div>
        </div>
      </template>
    </div>
    <div class="w100% h-50px position-absolute bottom-0 flex flex-justify-between flex-items-center">
      <div class="flex flex-items-end font-size-16px font-bold">合计：<span class="color-#e55343 font-size-20px">{{ curSelectedGood?.price || 0}}</span></div>
      <el-button type="primary" round @click="handleBuy">立即购买</el-button>
    </div>
  </div>
</template>
<!-- 改为rechargeCenter组件 -->
<script setup>
import { ref,onMounted } from 'vue'
const { ipcRendererChannel } = window
import { getGoodsList } from '@renderer/api/index'
const curSelectedGood = ref({})
const goodsList = ref([])
const loading = ref(false)
const getGoodsFun = () => {
  loading.value = true
  getGoodsList({platform: 'ios'}).then(res => {
    loading.value = false
    goodsList.value = res.data.package_list.map((item,index) => {
      item.title = item.title.replace('(','（ ')
      item.title = item.title.replace(')',' ）')
      curSelectedGood.value = item.list.find(inner => {
        return inner.is_default
      })
      return item
    })
  })
}
const handleBuy = () => {
  const productId = 'yfct_0001'
  ipcRendererChannel.purchaseStatus.on(handlePurchaseStatus)
  ipcRendererChannel.InAppPurchase.invoke(productId).then(res => {
    console.log(res)
  })
}
const handlePurchaseStatus = (event,data) => {
  let { status,productId,msg } = data
  switch(status){
    case 'purchasing':
      console.log(msg)
      console.log('transaction',data.transaction)
      break;
    case 'purchased':
      console.log(msg)
      break;
    case 'failed':
      console.log(msg)
      break;
  }
}
onMounted(()=>{
  getGoodsFun()
})
</script>

<style lang="scss" scoped>
.packageRecharge{
  .goodsOuter{
    height: calc(100% - 50px);
    border-bottom: 1px solid #F5F5F5;
  }
  .goodsOuter::-webkit-scrollbar-thumb {
    background: #cccccc;
  }
  .goodsOuter::-webkit-scrollbar-thumb:hover {
    background: #999999; /* 悬浮时可选更深一点的灰色 */
  }
  .tag{
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 8px 0 8px 0;
    font-size: 12px;
    width: 80px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
  }
  .redTag{
    color: white !important;
    background: linear-gradient(90deg, #E8A0A0 0%, #D67F7F 100%);
  }
  .yellowTag{
    color: #232323;
    background: linear-gradient(90deg, #FFE0A7 0%, #FFD282 100%);
  }
  .selected{
    background: rgba(0,195,244);
    color: white;
    font-weight: 500;
  }
}
</style>