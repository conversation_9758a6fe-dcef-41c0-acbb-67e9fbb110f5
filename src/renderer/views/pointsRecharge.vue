<template>
  <div class="pointsRecharge w-100% h-100% position-relative">
    <div class="overflow-auto goodsOuter flex flex-wrap" v-loading="loading">
      <div v-for="item in goodsList" :key="item.id"  @click="curSelectedGood = item" :class="{'selected': curSelectedGood?.id == item.id}" class="item flex flex-col flex-justify-center flex-items-center p-10px w-160px h-90px mr-20 mb-20 bg-#F5F5F5 rounded-8px cursor-pointer position-relative">
        <div class="font-size-16px font-500">{{ item.original_points }} 点</div>
        <div class="flex flex-items-center font-size-14px mt-2">￥{{ item.price }}</div>
        <div class="tag yellowTag" v-if="item.bonus_points != 0">送{{item.bonus_points}}点</div>
      </div>
    </div>
    <div class="w100% h-50px position-absolute bottom-0 flex flex-justify-between flex-items-center">
      <div class="flex flex-items-end font-size-16px font-bold">合计：<span class="color-#e55343 font-size-20px">{{ curSelectedGood?.price || 0}}</span></div>
      <el-button type="primary" round>立即购买</el-button>
    </div>
  </div>
</template>
<!-- 改为rechargeCenter组件 -->
<script setup>
import { ref,onMounted } from 'vue'
import { getGoodsList } from '@renderer/api/index'
const curSelectedGood = ref({})
const goodsList = ref([])
const loading = ref(false)
const getGoodsFun = () => {
  loading.value = true
  getGoodsList({platform: 'ios'}).then(res => {
    loading.value = false
    goodsList.value = res.data.points_list
    curSelectedGood.value = res.data.points_list.find((item,index) => {
      return item.is_default
    })
  })
}
onMounted(()=>{
  getGoodsFun()
})
</script>

<style lang="scss" scoped>
.pointsRecharge{
  .goodsOuter{
    height: calc(100% - 50px);
    border-bottom: 1px solid transparent;
  }
  .goodsOuter::-webkit-scrollbar-thumb {
    background: #cccccc;
  }
  .goodsOuter::-webkit-scrollbar-thumb:hover {
    background: #999999; /* 悬浮时可选更深一点的灰色 */
  }
  .tag{
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 8px 0 8px 0;
    font-size: 12px;
    width: 80px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
  }
  .redTag{
    color: white !important;
    background: linear-gradient(90deg, #E8A0A0 0%, #D67F7F 100%);
  }
  .yellowTag{
    color: #232323;
    background: linear-gradient(90deg, #FFE0A7 0%, #FFD282 100%);
  }
  .selected{
    background: rgba(0,195,244);
    color: white;
    font-weight: 500;
  }
}
</style>