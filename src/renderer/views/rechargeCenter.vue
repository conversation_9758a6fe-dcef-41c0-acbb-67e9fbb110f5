<template>
  <div class="packageRecharge w-100% h-100% position-relative">
    <div class="overflow-auto goodsOuter" v-loading="loading" v-if="subMenu.key == 'package'">
      <template v-for="item in goodsList" :key="item.title">
        <div class="title font-size-15px color-#333333 pb-20 font-bold">{{ item.title }}</div>
        <div class="list flex mb-20 flex-wrap">
          <div class="item flex flex-col flex-justify-center flex-items-center p-10px w-160px h-90px mr-20 mb-20 bg-#F5F5F5 rounded-8px cursor-pointer position-relative" v-for="inner in item.list" :key="inner.id" :class="{'selected': curSelectedGood?.id === inner.id}" @click="curSelectedGood = inner">
            <div class="font-size-16px font-500">{{ inner.duration_hours }} 小时</div>
            <div class="flex flex-items-center font-size-14px mt-2">￥{{ inner.price }}</div>
            <div v-if="inner.show_discount_icon" class="tag yellowTag">{{ inner.show_discount_icon ? '限时折扣' : '' }}</div>
            <div v-if="inner.show_new_discount_icon" class="tag yellowTag">{{ inner.show_new_discount_icon ? '新人专享' : '' }}</div>
          </div>
        </div>
      </template>
    </div>
    <div class="overflow-auto goodsOuter flex flex-wrap" v-loading="loading" v-else>
      <div v-for="item in goodsList" :key="item.id"  @click="curSelectedGood = item" :class="{'selected': curSelectedGood?.id == item.id}" class="item flex flex-col flex-justify-center flex-items-center p-10px w-160px h-90px mr-20 mb-20 bg-#F5F5F5 rounded-8px cursor-pointer position-relative">
        <div class="font-size-16px font-500">{{ item.original_points }} 点</div>
        <div class="flex flex-items-center font-size-14px mt-2">￥{{ item.price }}</div>
        <div class="tag yellowTag" v-if="item.bonus_points != 0">送{{item.bonus_points}}点</div>
      </div>
    </div>
    <div class="w100% h-50px position-absolute bottom-0 flex flex-justify-between flex-items-center">
      <div class="flex flex-col flex-items-start">
        <div class="font-size-16px font-bold">
          合计：<span class="color-#e55343 font-size-20px">{{ curSelectedGood?.price || 0}}</span>
        </div>
        <div class=" font-size-15px font-bold mt-5px" v-show="curSelectedGood?.saved_amount && curSelectedGood?.saved_amount != 0">
          立省：<span class="color-#e55343">{{ curSelectedGood?.saved_amount || 0}}</span>
        </div>
      </div>
      <el-button type="primary" round @click="handleBuy">立即购买</el-button>
    </div>
    <el-dialog v-model="payStatusPopShow" title="状态" width="500">
      <span class="font-size-16px font-bold">{{ payStatusText }}</span>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref,onMounted } from 'vue'
const { ipcRendererChannel } = window
import { getGoodsList,createOrder,toPay,applePay,getOrderDetail,getUserInfo } from '@renderer/api/index'
import { ElMessageBox } from 'element-plus'
const props = defineProps({
  subMenu: {
    type: Object,
    default: () => {}
  }
})
const curSelectedGood = ref({})
const goodsList = ref([])
const loading = ref(false)
const getGoodsFun = () => {
  loading.value = true
  getGoodsList({platform: 'ios'}).then(res => {
    loading.value = false
    if(props.subMenu.key == 'package'){
      goodsList.value = res.data.package_list.map((item,index) => {
        item.title = item.title.replace('(','（ ')
        item.title = item.title.replace(')',' ）')
        curSelectedGood.value = item.list.find(inner => {
          return inner.is_default
        })
        return item
      })
      if(res.data.new_models.length > 0){
        let obj = {
          title: '深度体验卡（ 新人专享 ）',
          list: res.data.new_models
        }
        goodsList.value.unshift(obj)
      }
    }else{
      goodsList.value = res.data.points_list
      curSelectedGood.value = res.data.points_list.find((item,index) => {
        return item.is_default
      })
    }
  })
}
const payStatusPopShow = ref(false)
const payStatusText = ref('')
const handleBuy = () => {
  if(!curSelectedGood.value?.id){
    ElMessage.error('请选择商品')
    return
  }
  loading.value = true
  if(window.systemInfo.platform == 'win32'){
    windowsPay()
    return
  }
  ipcRendererChannel.purchaseStatus.on(handlePurchaseStatus)
  ipcRendererChannel.InAppPurchase.invoke(curSelectedGood.value.apple_product_id).then(res => {
    if(!res.success && res.message){
      loading.value = false
      ElMessage.error(res.message)
    }
    console.log(res) 
  })
}
const handlePurchaseStatus = (event,data) => {
  let { status,productId,msg,receiptURL,transaction } = data
  switch(status){
    case 'purchasing':
      console.log(data)
      console.log('transaction',data.transaction)
      break;
    case 'purchased':
      console.log(data)
      appplePayFun(receiptURL)
      break;
    case 'failed':
      console.log(data)
      loading.value = false
      ElMessageBox.alert(`购买失败，${transaction.errorMessage}`, '温馨提示', {
        confirmButtonText: '确定',
        showCancelButton: false,
        type: 'error'
      }).then(() => {})
      .catch(() => {})
      break;
  }
}
const windowsPay = () => {
  createOrder({shop_id: curSelectedGood.value.shop_id}).then(res=>{
    if(res.code == 'YFF0'){
      let obj = {
        order_id: res.data.order_id,
        pay_type: 'pc'
      }
      toPay(obj).then(innerRes=>{
        if(res.code == 'YFF0'){
          const data = {
            url: innerRes.data.url,
          }
          ipcRendererChannel.OpenWin.invoke(data)
          ElMessageBox.confirm(
            '请问您是否支付完成?',
            {
              confirmButtonText: '支付完成',
              cancelButtonText: '取消',
              type: 'warning',
              callback: async (action) => {
                console.log(action)
                if(action === 'confirm'){
                  let payRes = await getOrderDetail({order_id: obj.order_id})
                  payRes.data.status == 0 ? payStatusText.value = '支付成功' : payStatusText.value = '支付失败'
                  payStatusPopShow.value = true
                  getUserInfo().then(userRes=>{
                    if(userRes.code == 'YFF0'){
                      localStorage.setItem('userInfo',JSON.stringify(userRes.data.user))
                    }
                  })
                  setTimeout(() => {
                    payStatusPopShow.value = false
                    window.location.reload()
                  }, 3000)
                }else{
                  window.location.reload()
                }
              }
            }
          )
        }
      })
    }
  })
}
let abortController = null
const appplePayFun = async (receiptURL) => {
  let receiptRes = await ipcRendererChannel.ReadFile.invoke({filePath: receiptURL,type: 'receipt'})
  let receipt_data = null
  if(receiptRes.success){
    receipt_data = receiptRes.content
  }else{
    ElMessage.error(receiptRes.message)
    return
  }
  if (abortController) {
    abortController.abort()
  }
  abortController = new AbortController()
  applePay({receipt_data, signal: abortController.signal}).then(res=>{
    if(res.code == 'YFF0'){
      loading.value = false
      ElMessageBox.alert('购买成功', '温馨提示', {
        confirmButtonText: '确定',
        showCancelButton: false,
        type: 'success'
      }).then(() => {})
      .catch(() => {})
    }else{
      loading.value = false
    }
  })
}
onMounted(()=>{
  getGoodsFun()
})
</script>

<style lang="scss" scoped>
.packageRecharge{
  .goodsOuter{
    height: calc(100% - 66px);  // -50正好接触到按钮 按钮距离底部间距是 10 + 6 = 16px  所以50同样 + 16 = 66px
    border-bottom: 1px solid #F5F5F5;
    box-shadow: inset 0 -10px 10px -10px rgba(0, 0, 0, 0.1);
  }
  .goodsOuter::-webkit-scrollbar-thumb {
    background: #cccccc;
  }
  .goodsOuter::-webkit-scrollbar-thumb:hover {
    background: #999999; /* 悬浮时可选更深一点的灰色 */
  }
  .tag{
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 8px 0 8px 0;
    font-size: 12px;
    width: 80px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
  }
  .redTag{
    color: white !important;
    background: linear-gradient(90deg, #E8A0A0 0%, #D67F7F 100%);
  }
  .yellowTag{
    color: #232323;
    background: linear-gradient(90deg, #FFE0A7 0%, #FFD282 100%);
  }
  .selected{
    background: rgba(0,195,244);
    color: white;
    font-weight: 500;
  }
}
</style>