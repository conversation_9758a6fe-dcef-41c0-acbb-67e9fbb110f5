<template>
  <div class="tcHistory overflow-auto position-relative">
    <div class="flex flex-items-center mt-10">
      <div class="w-120px h-30px rounded-50px bg-#F5F5F5 font-size-14px flex flex-items-center flex-justify-center cursor-pointer" :class="{'checked': curType === 'recharge'}" @click="changeType('recharge')">充值记录</div>
      <div class="w-120px h-30px rounded-50px bg-#F5F5F5 font-size-14px flex flex-items-center flex-justify-center ml-10px cursor-pointer" :class="{'checked': curType === 'use'}" @click="changeType('use')">消费记录</div>
    </div>
    <el-table v-if="curType === 'recharge'" v-loading="tableLoading" style="width: 100%" height="calc(100% - 114px)" :data="tableData" width="100%" class="mt-20px" header-row-class-name="tableHeader" empty-text="暂无数据">
      <el-table-column prop="purchase_time" label="充值时间" />
      <el-table-column prop="" label="商品名称" align="center" >
        <template #default="scope">
          <span>{{ scope.row.shop_name }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="category" label="商品类型" align="center" ></el-table-column>
      <el-table-column prop="" label="商品内容" align="center">
        <template #default="scope">
          <span v-if="scope.row.duration_hours">{{ scope.row.duration_hours }} 小时</span>
          <span v-else>{{ scope.row.points }} 点</span>
        </template>
      </el-table-column>
    </el-table>
    <el-table v-if="curType === 'use'" v-loading="tableLoading" style="width: 100%" height="calc(100% - 114px)" :data="tableData" width="100%" class="mt-20px" header-row-class-name="tableHeader" empty-text="暂无数据">
      <el-table-column label="消费类型">
        <template #default="scope">
          <span>{{ getServiceTypeFun(scope.row.service_type) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="消费时间">
        <template #default="scope">
          <span>{{ scope.row.created_at }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结算">
        <template #default="scope">
          <span style="color: #FF3362;">- {{ scope.row.service_type == 'simultaneous_interpretation' ? `${scope.row.duration_time} 秒` : `${scope.row.amount} 点`}}</span>
        </template>
      </el-table-column>
    </el-table>
    <div class="position-absolute bottom-0">
      <el-pagination @change="changePage" background layout="prev, pager, next" :total="total" :hide-on-single-page="false" v-model:current-page="curPage" :page-size="pageSize" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted,watch } from 'vue'
import { getRechargeHistory, getUseHistory } from '@renderer/api/index'
import { getServiceTypeFun } from '@renderer/utils/utils'
const props = defineProps({
  subMenu: {
    type: Object,
    default: () => ({})
  }
})
const curType = ref('recharge')
const changeType = (type) => {
  tableLoading.value = true
  curType.value = type
  tableData.value = []
  total.value = 0
  curPage.value = 1
  getList()
}
const tableData = ref([])
const tableLoading = ref(false)
const total = ref(0)
const pageSize = ref(10)
const getList = () => {
  tableLoading.value = true
  let obj = {
    page: curPage.value,
    page_size: pageSize.value,
  }
  let commonApi = curType.value === 'recharge' ? getRechargeHistory : getUseHistory
  commonApi(obj).then(res => {
    tableLoading.value = false
    tableData.value = res.data.results
    total.value = res.data.count
  })
}

const curPage = ref(1)
const changePage = (e) => {
  curPage.value = e
  getList()
}
onMounted(() => {
  props.subMenu.key == 'rechargeHistory' ? curType.value = 'recharge' : curType.value = 'use'
  getList()
})

</script>

<style lang="scss">
.tcHistory{
  width: 100%;
  height: 100%;
  .checked{
    background-color: #00c3f4 !important;
    color: #fff !important;
    box-shadow: 0 0 10px 2px rgba(0, 195, 244, 0.5) !important;
  }
  .tableHeader{
    background-color: #f5f5f5 !important;
    color: #333 !important;
  }
  .el-table__row{
    height: 46px;
  }
}
</style>