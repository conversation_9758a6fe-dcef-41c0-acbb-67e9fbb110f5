<template>
  <div class="tariffDetails w-100% h-100%">
    <div class="w-100% bg-#F5F5F5 rounded-8px pl-16px pr-16px pt-5px pb-5">
      <div v-for="item in dataList" :key="item.label" class="item flex flex-justify-between flex-items-center h-50px line-height-50px color-#333333 font-size-15px">
        <div>
          <span class="font-500">{{ item.label }}</span>
          <span class="font-12px">（ {{ item.desc }} ）</span>
        </div>
        <div class="font-500">{{ item.price }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const dataList = ref([
  {
    label: 'AI同传',
    desc: '基于高精同传，增加大模型实时矫正，智能纠错，按时长收费',
    price: '0.32 / 分钟',
  },
  {
    label: '高精同传',
    desc: '实时语音翻译，即时准确传达，按时长收费',
    price: '0.32 / 分钟',
  },
  {
    label: '图片翻译',
    desc: '识别图片文字并翻译成目标语言，快速准确',
    price: '0.20 / 张',
  },
  {
    label: '文档翻译',
    desc: '支持多种格式文档，按文字量计费（1中文=3字符）',
    price: '0.30 / 千字符',
  },
  {
    label: '音频转写',
    desc: '音频/视频内容一键转为文字，高效便捷',
    price: '0.10 / 分钟',
  },
  {
    label: '文本翻译',
    desc: '直接输入文字获取翻译结果，完全免费使用',
    price: '免费',
  }
])

</script>

<style lang="scss">
.tariffDetails{
  .item{
    border-bottom: 1px solid #dddddd;
  }
  .item:last-of-type{
    border: none;
  }
}
</style>