<template>
  <div class="tcHistory overflow-auto position-relative">
    <div class="flex flex-items-center flex-justify-between mt-10 h-30px">
      <div class="flex flex-items-center ml-5px">
        <div class="flex flex-items-center" v-if="tableType == 'local'">
          <div class="w-120px h-30px rounded-50px bg-#F5F5F5 font-size-14px flex flex-items-center flex-justify-center cursor-pointer" :class="{'checked': curType === 'ALL'}" @click="curType = 'ALL'">全部</div>
          <div class="w-120px h-30px rounded-50px bg-#F5F5F5 font-size-14px flex flex-items-center flex-justify-center ml-10px cursor-pointer" :class="{'checked': curType === 'gjtc'}" @click="curType = 'gjtc'">高精同传</div>
          <div class="w-120px h-30px rounded-50px bg-#F5F5F5 font-size-14px flex flex-items-center flex-justify-center ml-10px cursor-pointer" :class="{'checked': curType === 'aitc'}" @click="curType = 'aitc'">AI同传</div>
          <el-input v-model="searchValue" rounded placeholder="请输入文档名称" clearable class="searchInput"></el-input>
        </div>
      </div>
      <div class="w-120px h-30px rounded-6px flex flex-items-center flex-justify-center color-white bg-#00c3f4 cursor-pointer font-size-15px" style="border-radius: 50px;" @click="changeTableType">
        <el-icon color="#fff" size="18px" class="mr-6px" style="transform: rotate(90deg);"><Sort /></el-icon>
        <span class="font-500">{{ tableType == 'local' ? '云端记录' : '本地记录' }}</span>
      </div>
    </div>
    <el-table v-if="tableType == 'local'" v-loading="tableLoading" style="width: 100%" height="calc(100% - 114px)" :data="tableData" width="100%" class="mt-20px" header-row-class-name="tableHeader" empty-text="暂无数据">
      <el-table-column prop="file_name" label="文档名称" width="260px" show-overflow-tooltip fixed="left" >
        <template #default="scope">
          <div class="flex flex-items-center">
            <el-icon v-if="scope.row.is_uploaded" class="mr-6px" size="16px" color="#00c3f4"><UploadFilled /></el-icon>
            <div class="textCut">{{ scope.row.file_name }}</div>
            <el-icon v-show="scope.row.hover" class="cursor-pointer ml-6 ediBtn" size="16px" color="#00c3f4" @click="updateFileName(scope.row)"><Edit /></el-icon>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="类型" align="center" >
        <template #default="scope">
          {{ scope.row.type == 'gjtc' ? '高精同传' : 'AI同传' }}  <!-- 本地存的是 aitc/gjtc -->
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="180px" align="center" />
      <el-table-column prop="duration" label="时长" align="center">
        <template #default="scope">
          <span>{{ scope.row.duration ? scope.row.duration : '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="action" label="操作" width="150px" align="center" fixed="right">
        <template #default="scope">
          <div class="flex flex-items-center flex-justify-around color-#00c3f4">
            <!-- <div class="m-3px w-33% flex flex-items-center flex-justify-center">
              <div style="cursor: not-allowed;color: #cccccc;" v-if="scope.row.is_uploaded" @click="handleItemFun(scope.row,'unUpload')">已上传</div>
              <div class="color-#00c3f4 cursor-pointer" v-else>
                <div v-if="scope.row.isUploading" style="cursor: not-allowed;color: #cccccc;" class="w-80px flex flex-items-center"><el-icon><Loading /></el-icon>上传中...</div>
                <div v-else class="flex flex-items-center"  @click="handleItemFun(scope.row,'upload')">
                  <img class="w-16px h-16px mr-2" src="@renderer/assets/noVip.png" alt="">
                  上传
                </div>
              </div>
            </div> -->
            <div class="m-3px w-50% cursor-pointer" @click="handleItemFun(scope.row,'preview')">预览</div>
            <!-- <a class="m-3px" @click="handleItemFun(scope.row,'open')">查看文件</a> -->
            <div class="m-3px w-50% cursor-pointer" @click="handleItemFun(scope.row,'delete')">删除</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="action" label="VIP" width="100px" align="center" fixed="right">
        <template #header>
          <el-tooltip class="box-item" effect="dark" :content="userInfo?.membership.is_vip ? '尊贵的VIP，赶快上传本地记录，体验云端存储吧！' : '开通会员，即可上传至云端，实现多端共享~'" placement="left">
            <div class="flex flex-items-center flex-justify-center">
              <img v-if="userInfo?.membership?.is_vip" class="w-16px h-16px mr-2" src="@renderer/assets/Vip.png" alt="">
              <img v-else class="w-16px h-16px mr-2" src="@renderer/assets/noVip.png" alt="">
              <span>云端上传</span>
            </div>
          </el-tooltip>
        </template>
        <template #default="scope">
          <div class="flex flex-items-center flex-justify-center">
            <div v-if="scope.row.is_uploaded" style="cursor: not-allowed;color: #cccccc;" @click="handleItemFun(scope.row,'unUpload')">已上传</div>
            <div v-else class="color-#00c3f4 cursor-pointer">
              <div v-if="scope.row.isUploading" style="cursor: not-allowed;color: #cccccc;" class="w-80px flex flex-items-center"><el-icon><Loading /></el-icon>上传中...</div>
              <div v-else class="flex flex-items-center"  @click="handleItemFun(scope.row,'upload')">
                上传
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-table v-else v-loading="tableLoading" style="width: 100%" height="calc(100% - 114px)" :data="tableData" width="100%" class="mt-20px" header-row-class-name="tableHeader" empty-text="暂无数据">
      <el-table-column prop="title" label="文档名称" width="260px" show-overflow-tooltip fixed="left" >
        <template #default="scope">
          <div class="flex flex-items-center">
            <el-icon class="mr-6px" size="16px" color="#00c3f4"><UploadFilled /></el-icon>
            <div class="textCut">{{ scope.row.file_obj.title }}</div>
            <!-- <el-icon v-show="scope.row.hover" class="cursor-pointer ml-6 ediBtn" size="16px" color="#00c3f4" @click="updateFileName(scope.row)"><Edit /></el-icon> -->
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="类型" align="center" >
        <template #default="scope">
          <span v-if="scope.row.file_obj.type">{{ scope.row.file_obj.type.indexOf('ai') != -1 ? 'AI同传' : '高精同传' }}</span>  <!-- 云端存的是 ai/gj -->
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="上传时间" width="180px" align="center" >
        <template #default="scope">
          {{ scope.row.create_time ? dayjs(scope.row.create_time).format('YYYY-MM-DD HH:mm:ss') : '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="duration" label="时长" align="center">
        <template #default="scope">
          <span>{{ scope.row.file_obj?.duration ? dayjs.duration(scope.row.file_obj.duration, 'seconds').format('HH:mm:ss') : '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="action" label="操作" width="180px" align="center" fixed="right">
        <template #default="scope">
          <div class="flex flex-items-center flex-justify-around color-#00c3f4 cursor-pointer">
            <div class="m-3px w-33%" @click="handleItemFun(scope.row,'download')">下载</div>
            <div class="m-3px w-33%" @click="handleItemFun(scope.row,'preview')">预览</div>
            <!-- <a class="m-3px" @click="handleItemFun(scope.row,'open')">查看文件</a> -->
            <div class="m-3px w-33%" @click="handleItemFun(scope.row,'delete')">删除</div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="position-absolute bottom-0">
      <el-pagination @change="changePage" background layout="prev, pager, next" :total="total" :hide-on-single-page="false" v-model:current-page="curPage" :page-size="pageSize" />
    </div>
    <div v-if="isPreviewDialogShow" class="position-fixed top-0 left-0 w-100vw h-100vh z-999999999" style="background: rgba(0,0,0,0.8)">
      <div class="color-white font-bold font-size-20px w-100% flex flex-items-center flex-justify-center mt-30px">
        <span>{{ previewTitle }}</span>
        <el-icon class="position-absolute right-30px cursor-pointer" size="20px" @click="closePreviewPop"><Close /></el-icon>
      </div>
      <div class="audioTextOuter" ref="previewPop">
        <div class="textItem" ref="audioTextItemRef" v-for="item in audioResTextArr" :key="item" :class="[ item.active ? 'hightLight' : '']">{{ item.text }}</div>
      </div>
      <audio class="w-80% h-50px position-fixed bottom-10px left-50% translate-x--50%" ref="audioPlayer" @timeupdate="updateHLAudioText" :src="audioUrl" controls controlslist="nodownload"/>
    </div>
    <el-dialog v-model="isUpdateFileNameShow" title="修改文件名称" width="500">
      <el-input v-model="newFileName" placeholder="请输入新的文件名" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="isUpdateFileNameShow = false">取消</el-button>
          <el-button type="primary" @click="confirmUpdataFileName">确认</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="isVipPopupShow" class="vipPopup"  center :show-close="false">
      <div class="w-260px position-relative">
        <img class="w-260px" src="@renderer/assets/vipPopup.png" alt="">
        <el-button class="w-50% position-absolute bottom-30px left-50% translate-x--50%" type="primary" round @click="reciveVip">免费领取</el-button>
        <el-icon @click="isVipPopupShow = false" size="26px" color="#FFF" class="cursor-pointer position-absolute bottom--60px left-50% translate-x--50%"><CircleCloseFilled /></el-icon>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted,watch } from 'vue'
const { ipcRendererChannel, crash } = window
import axios from 'axios'
import { uploadFileForCloud,getCloudFileList,deleteCloudFile,getCloudFileUrl, subscribeVip, getUserInfo  } from '@renderer/api/index'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
dayjs.extend(duration)
import { getFormat,handleAudioText } from '@renderer/utils/utils'
import { Close,Edit,UploadFilled,Loading,Sort,CircleCloseFilled,CircleClose } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
const tableType = ref('local')
const curType = ref('ALL')
const tableData = ref([])
const tableLoading = ref(false)
const total = ref(0)
const pageSize = ref(10)
const getList = (val) => {
  tableLoading.value = true
  if(val) curPage.value = 1
  tableType.value == 'local' ? getLocalList() : getCloudList()
}
const getCloudList = () => {
  getCloudFileList({page: curPage.value,page_size: pageSize.value}).then(res => {
    tableData.value = res.data.results
    total.value = res.data.count
    tableLoading.value = false
  })
}
const getLocalList = () => {
  let obj = {
    page: curPage.value,
    pageSize: pageSize.value,
    fileName: searchValue.value || '',
    type: curType.value == 'ALL' ? '' : curType.value
  }
  ipcRendererChannel.GetHistory.invoke(obj).then(res => {
    console.log(res)
    if(res.success){
      tableData.value = res.records.list.map(item=>{
        item.created_at = dayjs(item.created_at).format('YYYY-MM-DD HH:mm:ss')
        item.duration = dayjs.duration(item.duration, 'seconds').format('HH:mm:ss')
        item.isUploading = false
        return item
      })
      total.value = res.records.pagination.total
      tableLoading.value = false
    }
  })
}
const audioResTextArr = ref([])
const audioUrl = ref('')
const isPreviewDialogShow = ref(false)
const previewLoading = ref(false)
const previewTitle = ref('')
const previewPop = ref(null)
const isVipPopupShow = ref(false)
const handleItemFun = async (row,handleType) => {
  if(handleType == 'preview'){
    curAudioTextItemIndex.value = 0
    audioResTextArr.value = []
    isPreviewDialogShow.value = true
    previewLoading.value = true
    if(tableType.value == 'local'){
      getFileToPreview(row.txt_timestamp_path,row.audio_path)
    }else{
      let filesRes = await ipcRendererChannel.GetFilesInFolder.invoke({u_s_id: userInfo.value.u_s_id.toString(), cloud: true})
      if(filesRes.success){
        if(filesRes.files.length > 0){
          let filesIds = filesRes.files.map(item=> item.name.replace('.',''))
          if(filesIds.includes(row.id.toString())){
            console.log('本地已下载')
            let filePath = filesRes.files.find(item=> item.name.replace('.','') == row.id.toString()).path
            let fileName = row.file_obj.title.replace(' ','-')
            // let txtPath = filePath + '/' + fileName + '.txt'
            // let audioPath = filePath + '/' + fileName + '.mp3'
            let txtPath = `${filePath}/${fileName}.txt`
            let audioPath = `${filePath}/${fileName}.mp3`
            getFileToPreview(txtPath,audioPath)
          }else{
            console.log('本地不存在，需要新下载')
            downloadFileToLocal(row,'preview')  //下载到本地并预览
          }
        }
      }
    }
    previewTitle.value = row.file_name || row.file_obj.title
    setTimeout(()=>{
      previewPop.value.addEventListener('scroll', handlePreviewPopScroll)
    },1000)
  }
  if(handleType == 'open'){
    console.log(row)
    ipcRendererChannel.OpenTheFolder.invoke({filePath: row.folder_path}).then(res => {
      console.log(res)
      if(res.success){
        console.log(res)
      }else{
        ElMessage.error(res.message)
      }
    })
  }
  if(handleType == 'delete'){
    let str = tableType.value == 'local' ? '确定删除该本地记录吗？删除后无法恢复！</br> 此操作不影响云端记录！' : '确定删除该云端记录吗？删除后无法恢复！</br> 此操作不影响本地记录！'
    ElMessageBox.confirm(str, '温馨提示', {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true
    }).then(() => {
      if(tableType.value == 'local'){
        console.log(row)
        ipcRendererChannel.DeleteRecord.invoke({recordItem: {id: row.id,folder_path: row.folder_path},type: 'local'}).then(res => {
          console.log(res)
          if(res.success){
            ElMessage.success('删除成功')
            getList('refresh')
          }else{
            ElMessage.error(res.message)
          }
        }).catch(err => {
          console.log(err)
        })
      }else{
        deleteCloudFile({collection_id: row.id}).then(res => {
          console.log(res)
          if(res.code == 'YFF0'){
            ipcRendererChannel.UpdateUploadStatus.invoke({id: row.file_obj.localId, isUploaded: 0})
            ElMessage.success('删除成功')
            ipcRendererChannel.DeleteRecord.invoke({recordItem: {cloud_id: row.id.toString(),u_s_id: userInfo.value.u_s_id.toString()},type: 'cloud'}).then(res => {
              // 不管是否删除成功 问题不大
              if(res.success){
                // ElMessage.success('删除成功')
                // getList('refresh')
              }else{
                // ElMessage.error(res.message)
              }
            })
            getList('refresh')
          }else{
            ElMessage.error(res.message)
          }
        })
      }
    }).catch((e) => {
      console.log('取消删除',e)
    })
  }
  if(handleType == 'upload'){
    if(!userInfo.value.membership.is_vip){
      isVipPopupShow.value = true
      return
    }
    row.isUploading = true
    let txtPromise = ipcRendererChannel.ReadFile.invoke({filePath: row.txt_path,type: 'txt'})
    let audioPromise = ipcRendererChannel.ReadFile.invoke({filePath: row.audio_path,type: 'audio',audioFormat: 'buffer'})
    Promise.all([txtPromise, audioPromise]).then(res => {
      if(res[0].success && res[1].success){
        let temp = {
          title: row.file_name,
          type: row.type.indexOf('ai') != -1 ? 'ai' : 'gj',  //兼容其他端  本地存储的是 aitc/gjtc
          duration: row.duration_seconds,
          localId: row.id,
          source_language: row.source_language,
          target_language: row.target_language
        }
        let txtBlob = new Blob([res[0].content], { type: "text/plain;charset=utf-8" })
        let audioBlob = new Blob([res[1].content], { type: "audio/mp3" })
        let obj = {
          txt_file: new File([txtBlob], new Date().getTime() + '.txt', { type: "text/plain;charset=utf-8" }),
          audio_file: new File([audioBlob], new Date().getTime() + '.mp3', { type: "audio/mp3" }),
          file_object: JSON.stringify(temp)
        }
        uploadFileForCloud(obj).then(res => {
          if(res.code == 'YFF0'){
            ElMessage.success('上传成功')
            ipcRendererChannel.UpdateUploadStatus.invoke({id: row.id, isUploaded: 1})
            getList('refresh')
          }else{
            ElMessage.error(res.message)
          }
          row.isUploading = false
        }).catch(err => {
          row.isUploading = false
          ElMessage.error(err.message)
        })
      }else{
        row.isUploading = false
        let msg = !res[0].success ? res[0].message : res[1].message
        ElMessage.error(msg)
      }
    })
  }
  if(handleType == 'download'){
    let filesRes = await ipcRendererChannel.GetFilesInFolder.invoke({u_s_id: userInfo.value.u_s_id.toString(), cloud: true})
    if(filesRes.success){
      console.log(filesRes)
      if(filesRes.files.length > 0){
        let filesIds = filesRes.files.map(item=> item.name.replace('.',''))
        if(filesIds.includes(row.id.toString())){
          console.log('本地已下载')
          let filePath = filesRes.files.find(item=> item.name.replace('.','') == row.id.toString()).path
          ipcRendererChannel.OpenTheFolder.invoke({filePath}).then(res => {
            if(res.success){
              console.log(res)
            }else{
              ElMessage.error(res.message)
            }
          })
          return
        }
      }
      downloadFileToLocal(row,'open')  //下载到本地并打开文件夹
    }else{
      ElMessage.error(res.message)
    }
  }
  if(handleType == 'unUpload'){
    ipcRendererChannel.UpdateUploadStatus.invoke({id: row.id, isUploaded: 0})
    getList('refresh')
  }
}
const getFileToPreview = (txtPath,audioPath) => {
  let txtPromise = ipcRendererChannel.ReadFile.invoke({filePath: txtPath,type: 'txt'})
  let audioPromise = ipcRendererChannel.ReadFile.invoke({filePath: audioPath,type: 'audio'})
  Promise.all([txtPromise, audioPromise]).then(res => {
    if(res[0].success && res[1].success){
      audioResTextArr.value = handleAudioText(res[0].content)
      audioUrl.value = res[1].content
    }else{
      let msg = !res[0].success ? res[0].message : res[1].message
      ElMessage.error(msg)
    }
    previewLoading.value = false
  })
}
const downloadFileToLocal = async (row,type) => {
  let { oss_url: txtUrl } = row.text_file
  let { oss_url: audioUrl } = row.audio_file
  let fileName = row.file_obj.title.replace(' ','-')
  let obj = {
    files: [
      {name: fileName + '.txt',url: txtUrl},
      {name: fileName + '.mp3',url: audioUrl}
    ],
    u_s_id: userInfo.value.u_s_id.toString(),
    cloud_id: row.id.toString()
  }
  let downloadRes = await ipcRendererChannel.DownloadFilesToHiddenFolder.invoke(obj)
  if(downloadRes.success){
    ElMessage.success('保存成功')
    if(type == 'open'){
      ipcRendererChannel.OpenTheFolder.invoke({filePath: downloadRes.folder}).then(res => {
        console.log(res)
        if(res.success){
          console.log(res)
        }else{
          ElMessage.error(res.message)
        }
      })
    }
    if(type == 'preview'){
      let txtPath = `${downloadRes.folder}/${fileName}.txt`
      let audioPath = `${downloadRes.folder}/${fileName}.mp3`
      getFileToPreview(txtPath,audioPath)
    }
  }
}
const isScrolling = ref(false)
const audioTextItemRef = ref(null)
const audioPlayer = ref(null)
const curAudioTextItemIndex = ref(0)
const updateHLAudioText = () => {
  let currentTime = audioPlayer.value?.currentTime
  if(!currentTime) return
  audioResTextArr.value.forEach((item,index) => {
    let nextStartTime = index < audioResTextArr.value.length - 1 ? audioResTextArr.value[index + 1].start : Infinity
    if(currentTime >= item.start && currentTime <= nextStartTime){
      item.active = true
      curAudioTextItemIndex.value = index
    }else{
      item.active = false
    }
  })
  if(isScrolling.value){
    return
  }
  console.log(curAudioTextItemIndex.value)
  // 只有在音频播放时才会触发滚动
  // if (!audioPlayer.value.paused) {
    const curEl = audioTextItemRef.value[curAudioTextItemIndex.value]
    if (curEl) {
      curEl.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      })
    }
  // }
}
const scrollTimeout = null
const handlePreviewPopScroll = () => {
  isScrolling.value = true
  if (scrollTimeout) clearTimeout(scrollTimeout)
  // 检测用户是否停止滚动
  scrollTimeout = setTimeout(() => {
    isScrolling.value = false // 标记用户停止滚动
  }, 2000) // 2 秒后恢复自动滚动
}
const closePreviewPop = () => {
  isPreviewDialogShow.value = false
  audioPlayer.value?.pause()
}
const isUpdateFileNameShow = ref(false)
const newFileName = ref('')
const confirmUpdataFileName = () => {
  ipcRendererChannel.UpdateFileName.invoke({id: curUpdateItem.value.id, newFileName: newFileName.value}).then(res => {
    console.log(res)
    if(res.success){
      ElMessage.success('修改成功')
      isUpdateFileNameShow.value = false
      getList('refresh')
    }else{
      ElMessage.error(res.message)
    }
  })
}
const curUpdateItem = ref(null)
const updateFileName = (row) => {
  isUpdateFileNameShow.value = true
  newFileName.value = row.file_name
  curUpdateItem.value = row
}
const curPage = ref(1)
const changePage = (e) => {
  curPage.value = e
  getList()
}
const changeTableType = () => {
  tableType.value = tableType.value == 'local' ? 'cloud' : 'local'
  tableData.value = []
  total.value = 0
  curPage.value = 1
  getList('refresh')
}
const reciveVip = () => {
  subscribeVip({plan_id: 1}).then(res=>{
    ElMessage.success('领取成功')
    isVipPopupShow.value = false
    getUserInfo().then(userRes=>{
      if(userRes.code == 'YFF0'){
        userInfo.value = userRes.data.user
        localStorage.setItem('userInfo',JSON.stringify(userInfo.value))
      }
    })
  })
}
const searchValue = ref('')
let debounceTimer = null
watch(searchValue, (val) => {
  console.log(val)
  if (debounceTimer) clearTimeout(debounceTimer)
  debounceTimer = setTimeout(() => {
    getList('refresh')
  }, 500)
})
watch(curType, (val) => {
  getList('refresh')
})
const userInfo = ref({})
const init = () => {
  getList()
  userInfo.value = JSON.parse(localStorage.getItem('userInfo'))
}
onMounted(() => {
  init()
})

</script>

<style lang="scss">
.tcHistory{
  width: 100%;
  height: 100%;
  .checked{
    background-color: #00c3f4 !important;
    color: #fff !important;
    box-shadow: 0 0 10px 2px rgba(0, 195, 244, 0.5) !important;
  }
  .tableHeader{
    background-color: #f5f5f5 !important;
    color: #333 !important;
  }
  .el-table__row{
    height: 46px;
  }
  .audioTextOuter{
    text-align: center;
    font-size: 16px;
    color: white;
    font-weight: 500;
    height: calc(100% - 130px); 
    // border: 1px solid black;
    margin-top: 10px;
    overflow: auto;
    .textItem{
      padding: 6px 0;
      white-space: pre-wrap;
    }
  }
  .hightLight{
    background-color: rgba(255,255,255,0.4) !important;
    border-radius: 8px;
    font-size: 18px;
  }
  // 实现鼠标悬浮在表格某行时 该行出现edit icon
  .el-table__body tr:hover .ediBtn {
    display: inline-block !important;
  }
  .ediBtn {
    display: none !important;
  }

  .textCut{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 260px;
  }
  .searchInput{
    width: 200px;
    margin-left: 30px;
    .el-input__wrapper{
      border-radius: 50px;
    }
  }
  .vipPopup{
    background: transparent;
    box-shadow: none;
    display: flex;
    justify-content: center;
    align-items: center;
    // border: 1px solid red;
  }
}
</style>