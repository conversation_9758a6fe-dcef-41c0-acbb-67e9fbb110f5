/*
 * 文本翻译
 * 1. 输入框输入内容，自动翻译
 * 2. 翻译结果显示在右侧
 */
<template>
  <div class="textTransition flex">
    <div class="left flex-1">
      <textarea name="" v-model="sourceVal" id="" placeholder="请输入内容"></textarea>
    </div>
    <div class="right flex-1">
      <textarea name="" v-model="targetVal" id="" readonly></textarea>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { debounce } from 'lodash'
import { translateText } from '@renderer/api/index'
const props = defineProps({
  menuData: {
    type: Object,
    default: () => {}
  },
  sourceLang: {
    type: Object,
    default: () => {}
  },
  targetLang: {
    type: Object,
    default: () => {}
  }
})
const sourceVal = ref('')
const targetVal = ref('')
const translateFun = debounce(() => {
  if(!sourceVal.value) return
  let obj = {
    text: sourceVal.value,
    target_language: props.targetLang.languageTarget,
    source_language: props.sourceLang.languageSource
  }
  translateText(obj).then((res: any) => {
    if (res.code == 'YFF0') {
      targetVal.value = res.data.translated_text
    }
  })
}, 1000)
watch(() => sourceVal.value, (newVal, oldVal) => {
  if(newVal){
    translateFun()
  }else{
    targetVal.value = ''
  }
},{ immediate: true })
watch(() => props.sourceLang, (newVal, oldVal) => {
  sourceVal.value = ''
  targetVal.value = ''
}, { immediate: true })
watch(() => props.targetLang, (newVal, oldVal) => {
  targetVal.value = ''
  translateFun()
}, { immediate: true })
</script>

<style scoped lang="scss">
.textTransition{
  width: 100%;
  height: 100%;
  border: 1px dashed #85D3E2;
  border-radius: 8px;
  .left{
    border-right: 1px dashed #85D3E2;
  }
  textarea{
    border: none;
    resize: none;
    outline: none;
    font-size: 16px;
    border-radius: 8px;
    overflow: auto;
    // border: 1px solid #85D3E2;
    width: 100%;
    height: 100%;
    padding: 12px;
  }
}
</style>