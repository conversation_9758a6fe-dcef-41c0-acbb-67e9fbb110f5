<template>
  <div class="tscy w-100% h-100% position-relative">
    <div ref="innerTextView" class="w-100% rounded-8px border-1px border-#85D3E2 border-solid box-sizing-border-box p-12px overflow-auto" :style="{'height': `calc(100% - 70px)`, 'font-size': `${fontSize}px`}">
      <!-- <span v-if="!resTextArr.length" class="color-#999999 font-size-14px">翻译内容显示区域......</span> -->
      <div v-for="(item,index) in resTextArr" :key="index">
        <div class="color-#000000 font-500">{{ item.originalText }}</div>
        <div class="color-#000000 font-500">{{ item.translatedText }}</div>
        <br>
      </div>
    </div>
    <div class="box-sizing-border-box text-align-center position-absolute bottom-0 left-50% translate-x--50%">
      <div v-if="!isRunning" class="flex flex-col flex-items-center flex-justify-center cursor-pointer" @click="startFun">
        <img class="w-40px h-40px" src="@renderer/assets/start.png" alt="">
        <span class="font-size-12px color-#333333 font-bold">开始</span>
      </div>
      <div v-else class="flex flex-col flex-items-center flex-justify-center cursor-pointer" @click="stopFun">
        <img class="w-40px h-40px" src="@renderer/assets/stop.png" alt="">
        <span class="font-size-12px color-#333333 font-bold">停止</span>
      </div>
    </div>
    <div class="position-absolute top--30px left-50% translate-x--50% flex flex-items-center flex-justify-center">
      <el-icon class="mr-6px" color="#999999"><Clock /></el-icon>
      <span class="color-#333333 font-500">{{ formatTime(seconds) }}</span>
    </div>
    <div class="flex flex-items-center flex-justify-center position-absolute bottom-15px right-0">
      <div class="flex flex-col flex-items-center flex-justify-center cursor-pointer mr-10px" @click="changeFontSize('add')">
        <el-tooltip placement="top" class="box-item" effect="dark" center trigger="hover" content="字体放大">
          <el-icon size="26px" color="#dddfe6"><CirclePlusFilled /></el-icon>
        </el-tooltip>
      </div>
      <div class="flex flex-col flex-items-center flex-justify-center cursor-pointer mr-30px" @click="changeFontSize('reduce')">
        <el-tooltip placement="top" trigger="hover" center content="字体缩小">
          <el-icon size="26px" color="#dddfe6"><RemoveFilled /></el-icon>
        </el-tooltip>
      </div>
      <div class="flex flex-items-center flex-justify-center">
        <el-switch v-model="floatWinStatus" @change="changeFloatWinSwitch" /> 
        <span class="font-size-14px color-#333333 font-bold ml-10">悬浮窗</span>
      </div>
      <!-- <el-icon class="ml-20px" color="#999999" size="22px"><Setting /></el-icon> -->
    </div>
    <!-- bg-#0008 -->
    <div v-if="tipsOverlay" class="position-absolute top-0px left-50% translate-x--50% w-100% rounded-8px flex flex-col flex-items-center flex-justify-center" style="height: calc(100% - 60px);">
      <div class="flex flex-items-center flex-justify-center">
        <span class="font-size-16px color-#333333 font-bold">同传剩余时长：</span>
        <span class="font-size-18px color-#333333 font-bold">{{ totalBalanceTime }}</span>
      </div>
      <span class="font-size-16px color-#333333 font-bold mt-30">选择收音模式：</span>
      <div class="flex">
        <div v-for="item in audioModeList" :key="item.value" @click="changeAudioModel(item)" :class="{'audioModelChecked':item.value == curAudioModel}" 
        class="color-#333333 cursor-pointer border-1px border-transparent border-solid p-10px w-200px h-100px rounded-8px bg-#EEEEEE flex flex-col flex-items-center flex-justify-center m-20px text-align-center">
          <div class="flex flex-items-center flex-justify-center">
            <img class="w-16px h-16px modelIcon" :src="item.icon" alt="">
            <span class="font-size-14px font-bold ml-5">{{ item.label }}</span>
          </div>
          <span class="font-size-13px mt-10px">{{ item.text }}</span>
        </div>
      </div>
      <template v-if="subMenu.key == 'aitc'">
        <span class="font-size-16px color-#333333 font-bold mt-30">选择模型：</span>
        <div class="flex">
          <div v-for="item in AIModelList" :key="item.value" @click="changeAIModel(item)" :class="{'audioModelChecked':item.value == curAIModel.value}" 
          class="color-#333333 cursor-pointer border-1px border-transparent p-10px w-140px h-40px rounded-8px bg-#EEEEEE flex flex-col flex-items-center flex-justify-center m-20px text-align-center">
            <div class="flex flex-items-center flex-justify-center">
              <img class="w-20px h-20px" :src="item.icon" alt="">
              <span class="font-size-14px ml-5">{{ item.label }}</span>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
const { ipcRendererChannel } = window
import { ref,onMounted, onUnmounted, inject } from 'vue'
import { useRouter } from 'vue-router'
import { Clock,Setting,CirclePlusFilled,RemoveFilled } from '@element-plus/icons-vue'
import debounce from 'lodash/debounce'
import dayjs from 'dayjs'
import * as SpeechSDK from "microsoft-cognitiveservices-speech-sdk"
import { formatTimestamp } from "@renderer/utils/utils"
import laba from '@renderer/assets/laba.png'
import maikefeng from '@renderer/assets/maikefeng.png'
import { translateText, getTcLang, getAzKey, startDeduct, recordKeepUp, stopDeduct, tcUpload, checkBalance, forceBegin } from '@renderer/api/index'
import { ElMessage } from 'element-plus'
import OpenAI from '@renderer/assets/OpenAI.png'
import DeepSeek from '@renderer/assets/DeepSeek.png'
import Tyqw from '@renderer/assets/Tyqw.png'
const updateRunningStatus = inject('updateRunningStatus')
const props = defineProps({
  targetLang: {
    type: Object,
    default: () => ({})
  },
  sourceLang: {
    type: Object,
    default: () => ({})
  },
  subMenu: {
    type: Object,
    default: () => ({})
  }
})
const emit = defineEmits()
const audioModeList = ref([
  {
    label: '麦克风',
    value: 'microphone',
    icon: maikefeng,
    text: '获取电脑外部声音，适用于线下上课，现场会议'
  },
  {
    label: '扬声器',
    value: 'speaker',
    icon: laba,
    text: '获取电脑内部声音，适用于网课，线上会议，音视频播放'
  }
])
const AIModelList = ref([
  {
    label: 'OpenAI',
    value: 'OpenAI',
    icon: OpenAI
  },
  {
    label: 'DeepSeek-V3',
    value: 'DeepSeek-V3',
    icon: DeepSeek
  },
  {
    label: '通义千问',
    value: 'Tyqw',
    icon: Tyqw
  }
])
const curAudioModel = ref('microphone')
const curAIModel = ref({
  label: 'OpenAI',
  value: 'OpenAI'
})
const floatWinStatus = ref(false)
const seconds = ref(0)
const formatTime = (sec) => {
  const h = String(Math.floor(sec / 3600)).padStart(2, '0')
  const m = String(Math.floor((sec % 3600) / 60)).padStart(2, '0')
  const s = String(sec % 60).padStart(2, '0')
  return `${h}:${m}:${s}`
}
let intervalId = null
const isRunning = ref(false)
const tipsOverlay = ref(true)
const startFun = async (val) => {
  fullLoading.value = ElLoading.service({
    lock: true,
    text: '',
    background: 'rgba(0,0,0,0.5)',
  })
  if(isRunning.value){
    ElMessage.warning('正在同声传译中，请勿重复操作')
    return
  }
  if (!isRunning.value) {
    // 开始先初始化各种变量
    seconds.value = 0
    resTextArr.value = []
    resTextForUpload.value = ''
    audioChunks.value = []
    
    //检查麦克风权限
    if(!await checkMicrophoneAccess()){
      fullLoading.value.close()
      return
    }
    let obj = {
      service_type: 'simultaneous_interpretation',
      characters_used: 0,
      source_name: props.sourceLang.languageName,
      target_name: props.targetLang.languageName,
      simultaneous_type: 'ai',
      is_pause: false,
    }
    // 开始计费
    let res = val == 'force' ? await forceBegin(obj) : await startDeduct(obj)
    if(res.code != 'YFF0'){
      fullLoading.value.close()
      if(res.code == 'YFF-1' && res.data.status_code == 'YFF099'){
        ElMessageBox.confirm(
          res.msg,
          '温馨提示',
          {
            confirmButtonText: `强制开始`,
            cancelButtonText: '取消',
            type: 'warning',
            callback: (action) => {
              if(action == 'confirm'){
                startFun('force')
              }
            }
          }
        )
      }
      return
    }else{
      console.log('开始计费')
    }
    tipsOverlay.value = false
    fullLoading.value.close()
    await initRecognizer()  // 初始化Az SDK 获取麦克风权限
    await startRecognition()  // 开始识别
    startRecording()  // 开始录音
    intervalId = setInterval(() => {
      seconds.value++
    }, 1000)
    isRunning.value = true
    updateRunningStatus(true)
    handleKeepRecord()  // 持续扣费 心跳接口
  }
  // const data = {
  //   url: '/audio',
  //   IsAudio: true,
  // }
  // ipcRendererChannel.OpenWin.invoke(data)
}
const recognizer = ref(null)
const resTextArr = ref([])
const resTextForUpload = ref('')
const initRecognizer = async() => {
  if (recognizer.value) {
    recognizer.value.close()  // 销毁旧实例
  }
  const speechTranslationConfig = SpeechSDK.SpeechTranslationConfig.fromSubscription(
    azConfig.value.key,
    azConfig.value.area
  )
  speechTranslationConfig.speechRecognitionLanguage = props.sourceLang.languageSource
  speechTranslationConfig.addTargetLanguage(props.targetLang.languageTarget)

  let audioConfig = SpeechSDK.AudioConfig.fromDefaultMicrophoneInput()
  recognizer.value = new SpeechSDK.TranslationRecognizer(speechTranslationConfig, audioConfig)
  // 监听识别结果  包含中间翻译结果
  resTextArr.value.push({
    originalText: '',
    translatedText: ''
  })
  recognizer.value.recognizing = (s, e) => {
    if(!isRunning.value){
      return
    }
    console.log('持续翻译中~',e.result.text,resTextArr.value)
    const originalText = e.result.text  // 获取原文
    const translatedText = e.result.translations.get(props.targetLang.languageTarget)
    resTextArr.value[resTextArr.value.length - 1].originalText = originalText
    resTextArr.value[resTextArr.value.length - 1].translatedText = translatedText
    ipcRendererChannel.WorkbenchWinToFloatWin.invoke(JSON.stringify({
      resTextArr: resTextArr.value
    }))
    handleScrollToBottom()
  }
  // 监听翻译结果 包含最终翻译结果
  recognizer.value.recognized = (s, e) => {
    console.log(e)
    console.log(SpeechSDK.ResultReason)  // 所有识别/翻译的状态
    const originalText = e.result.text  // 获取原文
    const translatedText = e.result.translations.get(props.targetLang.languageTarget)  // 获取译文
    const startTime = formatTimestamp(e.result.offset) // 计算开始时间
    const endTime = formatTimestamp(e.result.offset + e.result.duration) // 计算结束时间
    let textItem = `${startTime} --> ${endTime}\n${originalText}\n${translatedText}\n\n`
    resTextForUpload.value = resTextForUpload.value += textItem
    console.log(`${startTime} --> ${endTime}\n${originalText}\n${translatedText}`)
    
    resTextArr.value[resTextArr.value.length - 1] = {
      originalText: originalText,
      translatedText: translatedText
    };
    resTextArr.value.push({
      originalText: '',
      translatedText: ''
    })
    ipcRendererChannel.WorkbenchWinToFloatWin.invoke(JSON.stringify({
      resTextArr: resTextArr.value
    }))
    console.log(resTextArr.value)
    // textToSpeech(translatedText)  // 合成语音
    if (e.result.reason === SpeechSDK.ResultReason.NoMatch) {
      recognizer.value.stopContinuousRecognitionAsync()
      ElMessage.error('未检测到语音输入，请检查是否开启录音权限')
      stopFun()
      return
    }
    if (e.result.reason === SpeechSDK.ResultReason.Canceled) {
      const cancellation = SpeechSDK.CancellationDetails.fromResult(e.result)
      recognizer.value.stopContinuousRecognitionAsync()
      ElMessage.error(cancellation.errorDetails || '未知错误，请重试')
      stopFun()
      return
    }
  }
  
  recognizer.value.sessionStopped = (s, e) => {
    console.log("会话结束")
    recognizer.value.stopContinuousRecognitionAsync()
  }
  recognizer.value.canceled = (s, e) => {
    console.error('翻译失败:', e.errorDetails)
  }
}
const startRecognition = () => {
  if (!recognizer.value) {
    initRecognizer()
  }
  recognizer.value.startContinuousRecognitionAsync()
  console.log('开始录音 识别')
}
const mediaRecorder = ref(null)  // MediaRecorder 实例
const audioChunks = ref([])  // 存储录制的音频数据
// 开始录音
const startRecording = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true }) // 请求麦克风权限
    mediaRecorder.value = new MediaRecorder(stream)
    audioChunks.value = []
    mediaRecorder.value.ondataavailable = (event) => {
      // console.log('录音中~',event,event.data)
      if (event.data.size > 0) {
        audioChunks.value.push(event.data)
      }
    }
    mediaRecorder.value.start()
    console.log('开始录音')
  } catch (error) {
    console.error("录音失败:", error)
  }
}
const recordInterval = ref(null)
const handleKeepRecord = () => {
  if (isRunning.value) {
    recordInterval.value = setInterval(async () => {
      const totalLength = resTextArr.value.reduce((sum, item) => {
        return sum + item.originalText.length + item.translatedText.length;
      }, 0)
      let obj = {
        service_type: 'simultaneous_interpretation',
        characters_used: totalLength,
        source_name: props.sourceLang.languageName,
        target_name: props.targetLang.languageName
      }
      recordKeepUp(obj).then(res=>{
        if(res.code == 'YFF0'){
          console.log('持续扣费 心跳成功')
        }else{
          console.log('持续扣费 心跳失败')
          stopFun('keepupStop')
        }
      }).catch(()=>{
        ElMessage.error(res.msg)
        stopFun()
      })
    }, 25000)
  }else{
    clearInterval(recordInterval.value)
  }
}
const fullLoading = ref(null)
// 停止计时器
const stopFun = async (type) => {
  if(!isRunning.value){
    ElMessage.warning('没有正在进行中的同声传译任务')
    return
  }
  if (isRunning.value) {
    fullLoading.value = ElLoading.service({
      lock: true,
      text: '',
      background: 'rgba(0,0,0,0.5)',
    })
    clearInterval(intervalId)
    isRunning.value = false
    updateRunningStatus(false)
    await stopRecognition()  // az SDK 销毁实例
    const totalLength = resTextArr.value.reduce((sum, item) => {
      return sum + item.originalText.length + item.translatedText.length;
    }, 0)
    stopRecording()  // 停止录音
    clearInterval(recordInterval.value)  //清除 心跳定时函数

    let obj = {
      service_type: 'simultaneous_interpretation',
      characters_used: totalLength,
      source_name: props.sourceLang.languageName,
      target_name: props.targetLang.languageName
    }
    let res = await stopDeduct(obj)
    if(res.code == 'YFF0'){
      console.log('停止计费')
    }
    setTimeout(()=>{
      downloadFun(type)
    },1000)
  }
}
// 停止az识别
const stopRecognition =() => {
  if (recognizer.value) {
    recognizer.value.stopContinuousRecognitionAsync()
    recognizer.value.close()
    recognizer.value = null
    console.log('停止录音 识别')
  }
}
// 停止录音
const stopRecording = () => {
  if (mediaRecorder.value) {
    mediaRecorder.value.stop()
    console.log('停止录音')
  }
}
const router = useRouter()
const audioUrl = ref(null)  // 录制完成后的音频 URL
const duration = ref(0)
const downloadFun = async (type) => {
  fullLoading.value.close()
  const [hours, minutes, sec] = formatTime(seconds.value).split(":").map(Number)
  let audio_duration = duration.value = hours * 3600 + minutes * 60 + sec
  // let obj = {
  //   audio_duration,
  //   audio_file: new Blob(audioChunks.value, { type: "audio/wav" }),
  //   txt_file: new Blob([resTextForUpload.value], { type: "text/plain" })
  // }
  let userInfo = JSON.parse(localStorage.getItem('userInfo'))
  let timeStamp = new Date().getTime()
  let showFileName = `${props.sourceLang.languageName}-${props.targetLang.languageName}-${dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss')}`  //显示在列表的文件的名字
  let fileName = `${props.subMenu.key == 'gjtc' ? 'gjtc' : 'aitc'}_${timeStamp}`  //存在本地的文件的名字
  // 下载音频到本地
  let audioPromise = ipcRendererChannel.DownloadBlobFile.invoke({
    file: await new Blob(audioChunks.value, { type: "audio/mp3" }).arrayBuffer(),
    fileName: `${fileName}.mp3`,
    u_s_id: userInfo.u_s_id,
    filePathStr: timeStamp.toString()
  })
  // 下载时间戳文本到本地
  let txtTimestampPromise = ipcRendererChannel.DownloadBlobFile.invoke({
    file: await new Blob([resTextForUpload.value], { type: "text/plain" }).arrayBuffer(),
    fileName: `${fileName}_timestamp.txt`,
    u_s_id: userInfo.u_s_id,
    filePathStr: timeStamp.toString()
  })
  // 过滤时间戳文本
  let tempTextArr = resTextForUpload.value.split('\n')
  let downLoadTextRes = []
  tempTextArr.forEach((item,index) => {
    const timeRegex = /^(\d{2}:\d{2}:\d{2}\.\d{3}) --> (\d{2}:\d{2}:\d{2}\.\d{3})$/
    if(!timeRegex.test(item)){
      downLoadTextRes.push(item)
    }
  })
  let resStr = downLoadTextRes.join('\n')
  // 下载纯文本到本地
  let txtPromise = ipcRendererChannel.DownloadBlobFile.invoke({
    file: await new Blob([resStr], { type: "text/plain" }).arrayBuffer(),
    fileName: `${fileName}.txt`,
    u_s_id: userInfo.u_s_id,
    filePathStr: timeStamp.toString()
  })
  Promise.all([audioPromise,txtTimestampPromise,txtPromise]).then(res=>{
    if(res[0].success && res[1].success && res[2].success){
      console.log(res[0])
      // 两个文件下载到本地后向数据库插入一条数据
      ipcRendererChannel.AddRecord.invoke({
        folder_path: res[0].folderPath,
        audio_path: res[0].filePath,
        txt_timestamp_path: res[1].filePath,
        txt_path: res[2].filePath,
        origin_file_name: showFileName,
        file_name: showFileName,
        duration: audio_duration,
        duration_seconds: seconds.value,
        type: props.subMenu.key,
        source_language: props.sourceLang.languageName,
        target_language: props.targetLang.languageName,
        created_at: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss')
      }).then(res=>{
        if(res.success){
          console.log('插入数据库成功')
          ElMessageBox.confirm(
            `${type == 'keepupStop' ? '<strong style="color: red;">余额不足，本次任务自动结束</strong> \n' : ''}本次同声传译结束，稍后可在历史记录中查看。下次开始将清空屏幕内容并重新计时，请知晓~`,
            '温馨提示',
            {
              dangerouslyUseHTMLString: true,
              confirmButtonText: `${type == 'keepupStop' ? '立即充值' : '确定'}`,
              showCancelButton: type == 'keepupStop' ? true : false,
              cancelButtonText: '取消',
              type: 'warning',
              callback: (action) => {
                if(type == 'keepupStop' && action == 'confirm'){
                  emit('goPay')
                }
              }
            }
          )
        }else{
          console.log('插入数据库失败')
          ElMessage.error(res.message)
        }
      })
    }
  })
}
const innerTextView = ref(null)
const isAtBottom = () => {
  if (innerTextView.value) {
    const { scrollTop, scrollHeight, clientHeight } = innerTextView.value
    return scrollTop + clientHeight >= scrollHeight - 1 // 允许 1px 的误差
  }
  return false
};
const handleScrollToBottom = () => {
  if (innerTextView.value && !isAtBottom()) {
    innerTextView.value.scrollTo({
      top: innerTextView.value.scrollHeight, // 滚动到底部
      behavior: 'smooth', // 平滑滚动
    })
  }
}
const totalBalanceTime = ref(0)
const checkBalanceFun = () => {
  checkBalance({service_type: 'simultaneous_interpretation'}).then(res=>{
    if(res.code == 'YFF0' && res.data.status_code == 'YFF0'){
      totalBalanceTime.value = secondsToHMS(res.data.total_available_duration)
    }
  })
}
const secondsToHMS = (seconds) => {
  const hours = Math.floor(seconds / 3600);      // 计算小时
  const minutes = Math.floor((seconds % 3600) / 60); // 计算剩余分钟
  const secs = Math.floor(seconds % 60);         // 计算剩余秒数

  // 补零（确保两位数格式）
  const pad = (num) => num.toString().padStart(2, '0');
  return `${pad(hours)}:${pad(minutes)}:${pad(secs)}`;
}
const changeAudioModel = (item) => {
  curAudioModel.value = item.value
}
const changeAIModel = (item) => {
  curAIModel.value = item
  emit('setAIModel', item)
}
const checkMicrophoneAccess = async () => {
  try {
    let mediaAccessRes = await ipcRendererChannel.GetMediaAccessStatus.invoke()
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    console.log("用户同意使用麦克风")
    // stream.getTracks().forEach(track => track.stop()) // 关闭流，避免占用
    return mediaAccessRes
  } catch (error) {
    console.error("无法访问麦克风:", error)
    if (error.name === "NotAllowedError") {
      console.log("用户拒绝了麦克风权限")
      // ElMessage.error('用户拒绝了麦克风权限')
      ElMessageBox.confirm(
        '您已拒绝麦克风权限，请打开设置授权允许使用麦克风',
        '温馨提示',
        {
          confirmButtonText: '打开设置',
          cancelButtonText: '取消',
          type: 'warning',
          callback: (action) => {
            if(action == 'confirm'){
              ElMessage.success('正在打开设置页面~')
              ipcRendererChannel.OpenSystemSettings.invoke()
            }
          }
        }
      )
    } else if (error.name === "NotFoundError") {
      console.log("未检测到麦克风设备")
      ElMessage.error('未检测到麦克风设备')
    }else{
      ElMessage.error('无法访问麦克风')
    }
    return false
  }
}
const changeFloatWinSwitch = (e) => {
  if(e){
    ipcRendererChannel.OpenFloatWin.invoke({url: '/floatWin'}).then(res=>{
      if(res.success){
        console.log('窗口打开成功')
        setTimeout(()=>{
          ipcRendererChannel.WorkbenchWinToFloatWin.invoke(JSON.stringify({
            type: 'open',
            sourceLang: props.sourceLang,
            targetLang: props.targetLang,
            resTextArr: resTextArr.value,
            isRunning: isRunning.value,
            seconds: seconds.value,
            curAIModel: curAIModel.value,
            subMenu: props.subMenu,
          }))
        })
      }
    })
  }else{
    ipcRendererChannel.CloseFloatWin.invoke()
  }
}
const fontSize = ref(15)
const changeFontSize = (type) => {
  if(resTextArr.value.length == 0) return
  if(type === 'add'){
    fontSize.value++
  }else{
    fontSize.value--
  }
  console.log(fontSize.value)
}
const azConfig = ref({})
const getAzKeyFun = async () => {
  let res = await getAzKey()
  azConfig.value = res.data
}
const handleSetFloatWinSwitchStatus = (res) => {
  floatWinStatus.value = res
}
onMounted(()=>{
  checkBalanceFun()
  getAzKeyFun()
  emit('setAIModel', curAIModel.value)
  ipcRendererChannel.SetFloatWinSwitchStatus.on(handleSetFloatWinSwitchStatus)
})
onUnmounted(()=>{
  // 好像无用 会报错 TypeError: ipcRendererChannel.SetFloatWinSwitchStatus.off is not a function
  // ipcRendererChannel.SetFloatWinSwitchStatus.off(handleSetFloatWinSwitchStatus)
})
defineExpose({
  stopFun,
})
</script>

<style lang="scss" scoped>
.tscy {
  .audioModelChecked{
    // border: 2px solid rgba(0,0,0,0.6);
    box-shadow: 0 0 8px 1px rgba(255,255,255);
    background: rgba(0,195,244);
    color: white;
    .modelIcon{
      filter: none;
    }
  }
  .modelIcon{
    filter: invert(1) brightness(0);
  }
}
</style>