import { defineConfig, presetUno, presetAttributify, presetIcons } from 'unocss'

export default defineConfig({
  // 核心预设
  presets: [
    // 默认预设，包含流行的实用工具类
    presetUno(),
    // 属性化模式支持
    presetAttributify(),
    // 图标预设
    presetIcons({
      scale: 1.2,
      cdn: 'https://esm.sh/'
    }),
  ],
  // 自定义规则
  rules: [
    // 可以在这里添加自定义规则
    [/^m-(\d+)$/, ([, d]) => ({ margin: `${d}px` })],
    [/^mt-(\d+)$/, ([, d]) => ({ 'margin-top': `${d}px` })],
    [/^mb-(\d+)$/, ([, d]) => ({ 'margin-bottom': `${d}px` })],
    [/^ml-(\d+)$/, ([, d]) => ({ 'margin-left': `${d}px` })],
    [/^mr-(\d+)$/, ([, d]) => ({ 'margin-right': `${d}px` })],
    [/^p-(\d+)$/, ([, d]) => ({ padding: `${d}px` })],
    [/^pt-(\d+)$/, ([, d]) => ({ 'padding-top': `${d}px` })],
    [/^pb-(\d+)$/, ([, d]) => ({ 'padding-bottom': `${d}px` })],
    [/^pl-(\d+)$/, ([, d]) => ({ 'padding-left': `${d}px` })],
    [/^pr-(\d+)$/, ([, d]) => ({ 'padding-right': `${d}px` })],
    [/^w-(\d+)$/, ([, d]) => ({ width: `${d}px` })],
    [/^h-(\d+)$/, ([, d]) => ({ height: `${d}px` })],
    [/^w-(\d+)%$/, ([, d]) => ({ width: `${d}%` })],
    [/^h-(\d+)%$/, ([, d]) => ({ height: `${d}%` })],
    [/^w-(\d+)vw$/, ([, d]) => ({ width: `${d}vw` })],
    [/^h-(\d+)vh$/, ([, d]) => ({ height: `${d}vh` })],
    [/^rounded-(\d+)$/, ([, d]) => ({ 'border-radius': `${d}px` })],
    [/^rounded-t-(\d+)$/, ([, d]) => ({ 'border-top-left-radius': `${d}px`, 'border-top-right-radius': `${d}px` })],
    [/^rounded-b-(\d+)$/, ([, d]) => ({ 'border-bottom-left-radius': `${d}px`, 'border-bottom-right-radius': `${d}px` })],
    [/^rounded-l-(\d+)$/, ([, d]) => ({ 'border-top-left-radius': `${d}px`, 'border-bottom-left-radius': `${d}px` })],
    [/^rounded-r-(\d+)$/, ([, d]) => ({ 'border-top-right-radius': `${d}px`, 'border-bottom-right-radius': `${d}px` })],
    [/^font-size-(\d+)$/, ([, d]) => ({ 'font-size': `${d}px` })],
    [/^flex$/, () => ({ display: 'flex' })],
    [/^flex-col$/, () => ({ 'flex-direction': 'column' })],
    [/^flex-row$/, () => ({ 'flex-direction': 'row' })],
    [/^flex-col-reverse$/, () => ({ 'flex-direction': 'column-reverse' })],
    [/^flex-row-reverse$/, () => ({ 'flex-direction': 'row-reverse' })],
    [/^flex-wrap$/, () => ({ 'flex-wrap': 'wrap' })],
    [/^flex-nowrap$/, () => ({ 'flex-wrap': 'nowrap' })],
    [/^flex-items-center$/, () => ({ 'align-items': 'center' })],
    [/^flex-items-start$/, () => ({ 'align-items': 'flex-start' })],
    [/^flex-items-end$/, () => ({ 'align-items': 'flex-end' })],
    [/^flex-items-stretch$/, () => ({ 'align-items': 'stretch' })],
    [/^flex-justify-center$/, () => ({ 'justify-content': 'center' })],
    [/^flex-justify-start$/, () => ({ 'justify-content': 'flex-start' })],
    [/^flex-justify-end$/, () => ({ 'justify-content': 'flex-end' })],
    [/^flex-justify-between$/, () => ({ 'justify-content': 'space-between' })],
    [/^flex-justify-around$/, () => ({ 'justify-content': 'space-around' })],
    
  ],
  // 自定义快捷方式
  shortcuts: {
    // 例如: 'btn': 'py-2 px-4 font-semibold rounded-lg shadow-md'
  },
  // 主题配置
  theme: {
    colors: {
      // 可以在这里添加自定义颜色
    }
  }
})